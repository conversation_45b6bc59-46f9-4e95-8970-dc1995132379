import docSrc from '@/assets/images/fileMenu/<EMAIL>';
import folderSrc from '@/assets/images/fileMenu/<EMAIL>';
import form2Src from '@/assets/images/fileMenu/<EMAIL>';
import formSrc from '@/assets/images/fileMenu/<EMAIL>';
import modocSrc from '@/assets/images/fileMenu/<EMAIL>';
import moTablesSrc from '@/assets/images/fileMenu/<EMAIL>';
import pptSrc from '@/assets/images/fileMenu/<EMAIL>';
import spaceSrc from '@/assets/images/fileMenu/<EMAIL>';
import tableSrc from '@/assets/images/fileMenu/<EMAIL>';
import tableFormSrc from '@/assets/images/fileMenu/<EMAIL>';
import templateSrc from '@/assets/images/fileMenu/template.svg';
import testFormSrc from '@/assets/images/fileMenu/<EMAIL>';
import uploadFileSrc from '@/assets/images/fileMenu/<EMAIL>';
import { fm2 } from '@/modules/Locale';
const siderMenuCreateDocText = fm2('SiderMenu.siderMenuCreateDocText');
const siderMenuCreateMoDocText = fm2('SiderMenu.siderMenuCreateMoDocText');
const siderMenuCreateTableText = fm2('SiderMenu.siderMenuCreateTableText');
const siderMenuCreateMoTableText = fm2('SiderMenu.siderMenuCreateMoTableText');
const siderMenuCreatePptText = fm2('SiderMenu.siderMenuCreatePptText');
const siderMenuCreateFormText = fm2('SiderMenu.siderMenuCreateFormText');
const siderMenuCreateOrdinaryFormText = fm2('SiderMenu.siderMenuCreateOrdinaryFormText');
const siderMenuCreateTableFormText = fm2('SiderMenu.siderMenuCreateTableFormText');
const siderMenuCreateTestFormText = fm2('SiderMenu.siderMenuCreateTestFormText');
const siderMenuCreateFolderText = fm2('SiderMenu.siderMenuCreateFolderText');
const siderMenuCreateSpaceText = fm2('SiderMenu.siderMenuCreateSpaceText');
const siderMenuCreateUploadFileText = fm2('SiderMenu.siderMenuCreateUploadFileText');
const siderMenuCreateTemplateText = fm2('SiderMenu.siderMenuCreateTemplateText');
const siderMenuNew = fm2('SiderMenu.new'); // 新建
const siderMenuCreate = fm2('SiderMenu.siderMenuCreactText'); // 创建
const newDocText = siderMenuNew + siderMenuCreateDocText; // 新建文档
const newTableText = siderMenuNew + siderMenuCreateTableText; // 新建表格
const createFormText = siderMenuCreate + siderMenuCreateFormText; // 创建表单
const newFolderText = siderMenuNew + siderMenuCreateFolderText; // 新建文件夹
const tempCreateText = fm2('SiderMenu.siderMenuTemplateCreateText'); // 模板库创建
const fileUpload = fm2('SiderMenu.siderMenuCreateFileUploadText'); // 文件上传

// 自定义的value; 非后端返回;
export type FileType =
  | 'newdoc' // 文档
  | 'modoc' // 传统文档
  | 'mosheet' // 专业表格
  | 'table' // 表格
  | 'presentation' // 专业幻灯片
  | 'form' // 普通表单
  | 'table-form' // 表格视图表单
  | 'test-form' // 测验表单
  | 'folder' // 文件夹
  | 'space' // 团队空间
  | 'upload-file' // 上传文件
  | 'template'; // 从模板库创建

export interface FileItem {
  src: string;
  title: string;
  value: FileType;
  disabled?: boolean;
  children?: FileItem[];
  needModal?: boolean; // 是否需要弹出输入文件名的弹窗前置拦截, 目前仅支持H5;
}

const FILE_DOC = {
  src: docSrc,
  title: siderMenuCreateDocText,
  value: 'newdoc',
} satisfies FileItem;

const FILE_TRADITIONAL_DOC = {
  src: modocSrc,
  title: siderMenuCreateMoDocText,
  value: 'modoc',
} satisfies FileItem;

const FILE_TABLE = {
  src: tableSrc,
  title: siderMenuCreateTableText,
  value: 'mosheet',
} satisfies FileItem;

const FILE_APPLICATION_TABLE = {
  src: moTablesSrc,
  title: siderMenuCreateMoTableText,
  value: 'table',
} satisfies FileItem;

const FILE_PPT = {
  src: pptSrc,
  title: siderMenuCreatePptText,
  value: 'presentation',
} satisfies FileItem;

const FILE_ORDINARY_FORM = {
  src: form2Src,
  title: siderMenuCreateOrdinaryFormText,
  value: 'form',
} satisfies FileItem;

const FILE_TABLE_FORM = {
  src: tableFormSrc,
  title: siderMenuCreateTableFormText,
  value: 'table-form',
} satisfies FileItem;

const FILE_TEST_FORM = {
  src: testFormSrc,
  title: siderMenuCreateTestFormText,
  value: 'test-form',
} satisfies FileItem;

const FILE_FORM = {
  src: formSrc,
  title: siderMenuCreateFormText,
  value: 'form',
  children: [FILE_ORDINARY_FORM, FILE_TABLE_FORM, FILE_TEST_FORM],
} satisfies FileItem;

const FOLDER = {
  src: folderSrc,
  title: siderMenuCreateFolderText,
  value: 'folder',
} satisfies FileItem;

const SPACE = {
  src: spaceSrc,
  title: siderMenuCreateSpaceText,
  value: 'space',
} satisfies FileItem;

const UPLOAD_FILE = {
  src: uploadFileSrc,
  title: siderMenuCreateUploadFileText,
  value: 'upload-file',
} satisfies FileItem;

const TEMPLATE_LIB = {
  src: templateSrc,
  title: siderMenuCreateTemplateText,
  value: 'template',
} satisfies FileItem;

// for pc
export const FILE_LIST = [
  [FILE_DOC, FILE_TRADITIONAL_DOC, FILE_TABLE, FILE_APPLICATION_TABLE, FILE_PPT, FILE_FORM],
  [FOLDER, SPACE, UPLOAD_FILE],
] satisfies FileItem[][];

export const FILE_LIST_H5 = [
  { ...FILE_DOC, title: newDocText, needModal: true },
  { ...FILE_TABLE, title: newTableText, needModal: true },
  { ...FILE_ORDINARY_FORM, title: createFormText, src: formSrc, needModal: true },
  { ...FILE_APPLICATION_TABLE, needModal: true },
  { ...FOLDER, title: newFolderText, needModal: true },
  { ...TEMPLATE_LIB, title: tempCreateText },
  { ...UPLOAD_FILE, title: fileUpload },
] satisfies FileItem[];
