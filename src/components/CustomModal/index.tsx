import { Modal } from 'antd';
import type { ModalProps } from 'antd/lib';
import React from 'react';
import styled from 'styled-components';

import { ReactComponent as CheckCircleIconSVG } from '../../assets/images/modal/check-circle-icon.svg';
import { ReactComponent as WarningIconSVG } from '../../assets/images/modal/warning-icon.svg';

const StyledModal = styled(Modal)`
  .ant-modal-confirm-title {
    word-break: break-all;
  }

  .ant-modal-content button.ant-modal-close {
    left: unset;
    right: 12px;
  }

  .ant-modal-footer > .ant-btn + .ant-btn[type='button'] {
    margin-right: 0;
    margin-left: 8px;
  }
`;

const StyledContainer = styled.div<{ $hasPaddingLeft?: boolean }>`
  padding-left: unset;
`;

const StyledTitle = styled.div`
  display: flex;
  align-items: flex-start;
`;

export const StyledWarningIconSVG = styled(WarningIconSVG)`
  color: #f5d57a;
  height: 22px;
  margin-right: 16px;
  flex-shrink: 0;
`;

export const StyledConfirmIconSVG = styled(WarningIconSVG)`
  color: #5ba0e7;
  height: 22px;
  margin-right: 16px;
  flex-shrink: 0;
`;

export const StyledCheckIconSVG = styled(CheckCircleIconSVG)`
  height: 22px;
  margin-right: 16px;
  flex-shrink: 0;
`;

export const CustomModal: React.FC<
  ModalProps & {
    modalType: 'warning' | 'confirm' | 'check';
    hasPaddingLeft?: boolean;
  }
> = ({ modalType, hasPaddingLeft = true, title, children, ...props }) => {
  const getIcon = () => {
    if (modalType === 'warning') {
      return <StyledWarningIconSVG />;
    } else if (modalType === 'confirm') {
      return <StyledConfirmIconSVG />;
    } else if (modalType === 'check') {
      return <StyledCheckIconSVG />;
    }
    return null;
  };
  const Title = (
    <StyledTitle>
      {getIcon()}
      <div style={{ wordBreak: 'break-all', marginRight: 16 }}>{title}</div>
    </StyledTitle>
  );

  return (
    <StyledModal title={Title} {...props}>
      <StyledContainer $hasPaddingLeft={hasPaddingLeft}>{children}</StyledContainer>
    </StyledModal>
  );
};
