import { CaretDownOutlined, LeftOutlined, PlusOutlined } from '@ant-design/icons';
import CollaboratorsClient, { CollaboratorsClientEvents } from '@shimo/collaborators-client';
import type { InputRef } from 'antd';
import { Input, message, Popover, Tooltip } from 'antd';
import classNames from 'classnames';
import { useEffect, useRef, useState } from 'react';
import { history, useParams } from 'umi';

import * as fileApi from '@/api/File';
import { to } from '@/api/Request';
import starIcon from '@/assets/images/editor/<EMAIL>';
import { ReactComponent as CancelStarIcon } from '@/assets/images/svg/cancelStar.svg';
import { ReactComponent as DemoIcon } from '@/assets/images/svg/demo.svg';
import { ReactComponent as HistoryIcon } from '@/assets/images/svg/history.svg';
import { ReactComponent as MoreIcon } from '@/assets/images/svg/more.svg';
import { onSocketReady, resetSocket } from '@/components/EditorSDK/utils/socketManager';
import deleteConfirm from '@/components/fileList/components/deleteConfirm';
import { CreateFolder } from '@/components/Modal/CreateFolder';
import { useFileDetail } from '@/hooks/useFileDetail';
import useFileUpload from '@/hooks/useFileUpload';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { fm, fm2 } from '@/modules/Locale';
import { editorStore } from '@/store/Editor';
import type { FileAncestors, FileDetail } from '@/types/api';
import { downloadFile } from '@/utils/file';
import { getSMEditor } from '@/utils/ShimoSDK';

import { AddNewPopover } from '../AddNewPopover';
import type { AvatarItem } from '../AvatarGroup';
import { AvatarGroup } from '../AvatarGroup';
import { BackToPopover } from '../BackToPopover';
import CollaborationShare from '../Collaboration';
import { FileMenuPopover } from '../FileMenuPopover';
import { FilePathPicker } from '../Modal/FilePathPicker';
import { SaveStatus } from './SaveStatus';
import css from './style.less';

export function EditorHeader({ data }: { data?: FileDetail }) {
  const params = useParams<{ guid: string }>();
  const guid = params?.guid || '';

  // 收藏按钮
  const [isStar, setIsStar] = useState(false);
  // 存储文件详情数据
  const [fileDetail, setFileDetail] = useState({} as FileDetail);
  // 协作者列表
  const [collaborators, setCollaborators] = useState<AvatarItem[]>([]);
  // 最近使用文件列表
  const [recentFiles, setRecentFiles] = useState<FileDetail[]>([]);
  // 最近的祖先
  const [lastAncestor, setLastAncestor] = useState<FileAncestors | undefined>(undefined);
  // 控制文件菜单弹框显示
  const [fileMenuVisible, setFileMenuVisible] = useState(false);
  // 正在收藏中的状态
  const [isStaring, setIsStaring] = useState(false);

  const [messageApi, contextHolder] = message.useMessage();

  const isOnline = useNetworkStatus();

  useEffect(() => {
    if (!isOnline) {
      messageApi.info(
        <span>
          {fm2('Editor.saveStatus.offline')}。
          <a
            className="know"
            href="javascript:void(0)"
            onClick={() => {
              messageApi.destroy();
            }}
          >
            {fm2('Editor.ok')}
          </a>
        </span>,
        0,
      );
    }
  }, [isOnline]);

  // 实现标题输入框
  const [title, setTitle] = useState('');
  const [titleWidth, setTitleWidth] = useState(46);
  const spanRef = useRef<HTMLSpanElement>(null);
  const inputRef = useRef<InputRef>(null);
  const [shareVisible, setShareVisible] = useState(false);

  const { openFileDetail } = useFileDetail();

  // 国际化文案
  const inputPlaceholder = fm('Header.inputPlaceholder');
  const backButtonTipText = fm('Header.backButtonTipText');
  const backToButtonTipText = fm('Header.backToButtonTipText');
  const createButtonTipText = fm('Header.createButtonTipText');
  const teamButtonText = fm('Header.teamButtonText');
  const shareButtonText = fm('Header.shareButtonText');
  const fileMenuButtonTipText = fm('Header.fileMenuButtonTipText');
  const unfavorited = fm('Header.unfavorited');
  const favorited = fm('Header.favorited');
  const favorite = fm('Header.favorite');
  const unfavorite = fm('Header.unfavorite');
  const loadingText = fm('Common.loadingText');
  const historyButtonText = fm('Header.historyButtonText');
  const demoButtonText = fm('Header.demoButtonText');

  const i18nText = {
    delete: fm('File.delete'),
    title: fm('deleteConfirm.title'),
    content: fm('deleteConfirm.content'),
    okText: fm('deleteConfirm.title'),
    cancelText: fm('deleteConfirm.cancel'),
    action: fm('File.delete'),
    success: fm('deleteConfirm.success'),
    error: fm('deleteConfirm.error'),
    noSupport: fm('Editor.noSupport'),
    copy: fm('FilePathPicker.copy'),
  };

  interface UserEnterData {
    collaborators: AvatarItem[];
    enterUsers: AvatarItem[];
    data: {
      fileGuid: string;
      type: string;
      userInfo: AvatarItem;
    };
  }

  function handleUserEnter(data: UserEnterData) {
    const { collaborators } = data;
    setCollaborators(collaborators);
  }

  // 初始化协作者
  function initCollaborators(socket: SocketIOClient.Socket) {
    const collaboratorsClient = new CollaboratorsClient({
      ws: socket,
      fileGuid: window.file.guid,
      currentUserId: window.user.id,
    });
    collaboratorsClient.start();
    collaboratorsClient.on(CollaboratorsClientEvents.UserEnter, handleUserEnter);

    return collaboratorsClient;
  }

  useEffect(() => {
    let collaboratorsClient: CollaboratorsClient | null = null;

    onSocketReady((socket) => {
      collaboratorsClient = initCollaborators(socket);
    });

    return () => {
      // 退出协作者客户端
      if (collaboratorsClient) {
        collaboratorsClient.quit();
      }
      // 重置 socket
      resetSocket();
    };
  }, []);
  // 获取最近使用的文件
  async function getRecentFiles() {
    const [, res] = await to(fileApi.files({ type: 'used', limit: 20 }));
    if (res?.status === 200) {
      const fileData = res.data.list as FileDetail[];
      setRecentFiles(fileData.slice(0, 5));
    }
  }

  function showDownTip() {
    messageApi.open({
      type: 'loading',
      content: loadingText,
      duration: 0,
      style: {
        marginTop: '50px',
      },
    });
  }

  // 导出表格文件
  async function exportTableFile() {
    showDownTip();
    const [, res] = await to(fileApi.exportTable(guid));
    if (res?.status === 200) {
      downloadFile(res.data.downloadUrl, `${fileDetail.name}`);
    }
    messageApi.destroy();
  }

  // 轮训获取导出进度
  async function getExportProgress(taskId: string) {
    const [, res] = await to(fileApi.exportProgress({ taskId }));
    if (res?.status !== 200) return messageApi.destroy();

    const { progress, downloadUrl } = res.data.data;
    if (progress < 100) {
      setTimeout(() => {
        getExportProgress(taskId);
      }, 1500);
    } else if (progress === 100) {
      downloadFile(downloadUrl, `${fileDetail.name}`);
      messageApi.destroy();
    }
  }

  // 导出不同类型文件
  async function exportFile(exportType: string) {
    if (fileDetail.type === 'modoc' && ['imagePdf', 'image'].includes(exportType)) {
      getSMEditor()?.docsApi.pluginManager.getInstance('Paint').exporter.export(exportType);
      return;
    }
    if (fileDetail.type === 'mosheet' && ['pdf', 'image'].includes(exportType)) {
      if (exportType === 'pdf') {
        getSMEditor()?.exportPdf();
      } else {
        getSMEditor()?.exportImage();
      }
      return;
    }
    if (fileDetail.type === 'presentation' && ['imagePdf', 'image'].includes(exportType)) {
      getSMEditor()?.export?.(exportType);
      return;
    }
    showDownTip();
    const [err, res] = await to(fileApi.exportFile(guid, { type: exportType }));
    if (res?.status !== 200) {
      messageApi.destroy();
      message.error(err?.data?.msg);
      return;
    }

    const { taskId } = res.data.data;
    getExportProgress(taskId);
  }

  // 删除文件
  function deleteFile() {
    deleteConfirm({
      i18nText,
      data: guid,
      api: fileApi.deleteFile,
      callback: () => {
        history.push('/desktop');
      },
    });
  }

  // 使用文件上传 hook
  const { triggerUpload } = useFileUpload({
    parentGuid: lastAncestor?.guid,
  });

  useEffect(() => {
    if (data) {
      // 查看文件详情
      const viewFileDetail = (fileDetail: FileDetail) => {
        setFileDetail(fileDetail);
        setIsStar(fileDetail.starred);
        setTitle(fileDetail.name);
        editorStore.getState().update({ fileType: fileDetail.type });
      };
      viewFileDetail(data);
    }
  }, [data]);

  useEffect(() => {
    // 用户打开文件动作
    const userAction = async () => {
      await to(fileApi.userAction(guid));
      getRecentFiles();
    };

    // 获取最近的文档祖先，返回结果数组中要过滤掉自己，最后一个就是所需的最近祖先
    const getLastAncestor = async () => {
      const [, res] = await to(fileApi.getAncestors(guid));
      if (res?.status === 200) {
        const ancestors = res.data.ancestors.filter((item: FileAncestors) => item.guid !== guid);
        const lastAncestor = ancestors[ancestors.length - 1];
        setLastAncestor(lastAncestor);
      }
    };

    userAction();
    getLastAncestor();
  }, [guid]);

  // 根据输入内容调整宽度
  useEffect(() => {
    const current = spanRef.current;
    if (current) {
      if (!current.textContent) {
        setTitleWidth(46);
        return;
      }
      // 设置测量元素的内容
      current.textContent = title || inputPlaceholder;
      // 获取文本宽度，添加一些额外宽度用于输入框的padding
      const spanWidth = spanRef.current.offsetWidth + 5;
      // 确保宽度在46-240px之间
      const targetWidth = Math.max(46, Math.min(240, spanWidth));
      setTitleWidth(targetWidth);
    }
  }, [title, inputPlaceholder]);

  function handleFocus() {
    if (title === inputPlaceholder) {
      setTitle('');
    }
  }

  function handleChange(e: React.ChangeEvent<HTMLInputElement>) {
    const newValue = e.target.value;
    // 禁止第一个字符为空格
    if (newValue[0] === ' ') return;

    // 限制输入不超过100个字符
    if (newValue.length <= 100) {
      setTitle(newValue);
    }
  }

  // 重命名文件
  async function rename(newTitle?: string) {
    const titleToUse = newTitle || title || inputPlaceholder;
    const [, res] = await to(fileApi.rename(guid, titleToUse));
    if (res?.status === 200) {
      setFileDetail({
        ...fileDetail,
        name: titleToUse,
      });
      getRecentFiles();
      getSMEditor()?.setTitle?.(titleToUse);
    }
  }

  // 处理失去焦点
  function handleBlur() {
    if (!title) {
      setTitle(inputPlaceholder);
      if (fileDetail.name !== inputPlaceholder) {
        rename(inputPlaceholder);
      }
      return;
    }
    if (title !== fileDetail.name) {
      setTitle(title);
      rename(title);
    }
  }

  // 处理确认输入
  function handlePressEnter() {
    handleBlur();
    inputRef.current?.blur();
  }

  // 切换收藏
  async function toggleStar() {
    if (isStaring) return;
    setIsStaring(true);

    if (isStar) {
      // 取消收藏
      const [, res] = await to(fileApi.cancelStar(guid));
      if (res?.status === 204) {
        setIsStar(false);
        message.info(`「${fileDetail?.name}」${unfavorited}`);

        // 更新文件详情中的标签
        setFileDetail({
          ...fileDetail,
          starred: false,
        });
      }
    } else {
      // 收藏
      const [, res] = await to(fileApi.star(guid));
      if (res?.status === 204) {
        setIsStar(true);
        message.success(`「${fileDetail?.name}」${favorited}`);

        // 更新文件详情中的标签
        setFileDetail({
          ...fileDetail,
          starred: true,
        });
      }
    }
    // 处理完成，重置状态
    setIsStaring(false);
  }

  // 打开创建文件夹弹窗
  function createFolder() {
    CreateFolder({
      onOk: (name) => {
        window.open(`/api/v1/files/create/folder?parentGuid=${lastAncestor?.guid}&name=${name}`, '_self');
      },
    });
  }

  // 保存版本
  function saveVersion() {
    CreateFolder({
      type: 'saveVersion',
      onOk: (name) => {
        getSMEditor()?.createRevision({ name });
      },
    });
  }

  function openFilePathPicker(type: string) {
    if (type === 'create') {
      FilePathPicker({
        locationGuid: 'desktop',
        source: [
          {
            name: fileDetail.name,
            fileGuid: guid,
            isAdmin: fileDetail.isAdmin || fileDetail.isFileAdmin,
            role: fileDetail.role,
          },
        ],
        onOk: () => {},
      });
    } else {
      FilePathPicker({
        type: 'move',
        locationGuid: 'desktop',
        source: [
          {
            name: fileDetail.name,
            fileGuid: guid,
            isAdmin: fileDetail.isAdmin || fileDetail.isFileAdmin,
            role: fileDetail.role,
          },
        ],
        onOk: () => {},
      });
    }
  }

  function createFile(fileType: string, formType?: string) {
    let url = `/api/v1/files/create/${fileType}?parentGuid=${lastAncestor?.guid}&name=${inputPlaceholder}`;
    if (formType) {
      url += `&formType=${formType}`;
    }
    window.open(url, '_self');
  }

  function openFile(fileInfo: { fileType?: string; fileGuid?: string }) {
    let type = fileInfo.fileType || '';
    if (['newdoc', 'doc'].includes(type)) {
      type = 'docs';
    } else if (['modoc', 'docx'].includes(type)) {
      type = 'docx';
    } else if (['mosheet', 'xls', 'xlsx', 'excel', 'csv'].includes(type)) {
      type = 'sheets';
    } else if (['form'].includes(type)) {
      type = 'forms';
    } else if (['table'].includes(type)) {
      type = 'tables';
    } else if (['presentation', 'ppt', 'pptx'].includes(type)) {
      type = 'presentation';
    } else if (['img', 'mp3', 'mp4', 'mov', 'qt', 'wps', 'zip', 'rar'].includes(type)) {
      type = 'files';
    }
    if (fileInfo.fileGuid) {
      window.open(`/${type}/${fileInfo.fileGuid}`, '_self');
    }
  }

  function back() {
    // 确定目标路径：对上级目录没有权限时或者上级目录是桌面时，返回桌面，否则进入上级文件夹
    const targetPath = !lastAncestor || lastAncestor.guid === 'Desktop' ? '/desktop' : `/folder/${lastAncestor.guid}`;
    // 使用 location.href，解决返回上一级后，sdk右侧弹框没有关闭的问题
    location.href = targetPath;
  }

  interface MenuItem {
    key: string;
    exportType?: string;
    fileType?: string;
    fileGuid?: string;
    noSupport?: boolean;
  }
  // 处理导航项点击
  function handleMenuItemClick({ key, exportType, fileType, fileGuid, noSupport }: MenuItem) {
    setFileMenuVisible(false);
    if (noSupport) {
      message.warning(i18nText.noSupport);
      return;
    }
    if (key.includes('down') && exportType) {
      exportFile(exportType);
      return;
    }
    // 处理创建文件类型
    const createFileTypes = [
      'newdoc',
      'modoc',
      'mosheet',
      'presentation',
      'table',
      'normalForm',
      'tableViewForm',
      'quizForm',
      'template',
    ];
    if (createFileTypes.includes(key)) {
      if (['normalForm', 'tableViewForm', 'quizForm'].includes(key)) {
        const formType = key === 'normalForm' ? '' : key === 'tableViewForm' ? 'table' : 'quiz';
        return createFile('form', formType);
      }
      return createFile(key);
    }
    if (key === 'upload') {
      return triggerUpload();
    }
    if (key === 'uploadFolder') return;

    // 处理导航
    switch (key) {
      case 'back':
        back();
        break;
      case 'folder':
        createFolder();
        break;
      case 'myDesktop':
        history.push('/desktop');
        break;
      case 'workbench':
        history.push('/recent');
        break;
      case 'recent':
        openFile({ fileType, fileGuid });
        break;
      case 'favorite':
        toggleStar();
        break;
      case 'downToExcel':
        exportTableFile();
        break;
      case 'delete':
        deleteFile();
        break;
      case 'viewCommentList':
      case 'viewComment':
        getSMEditor()?.showComments();
        break;
      case 'addComment':
        getSMEditor()?.addComment();
        break;
      case 'fileInfo':
        openFileDetail({ guid, type: fileDetail.type });
        break;
      case 'print':
        if (fileDetail.type === 'modoc') {
          getSMEditor()?.printAll();
          return;
        }
        getSMEditor()?.print();
        break;
      case 'saveVersion':
        if (['modoc', 'presentation'].includes(fileDetail.type)) {
          saveVersion();
          return;
        }
        getSMEditor()?.createRevision();
        break;
      case 'viewHistory':
        getSMEditor()?.showHistory();
        break;
      case 'startDemo':
        getSMEditor()?.startDemonstration();
        break;
      case 'createCopy':
        openFilePathPicker('create');
        break;
      case 'move':
        openFilePathPicker('move');
        break;
    }

    setFileMenuVisible(false);
  }

  function handleMenuItemOpenInNewTab() {}

  // 文件菜单弹框显示状态变化
  function fileMenuOpenChange(visible: boolean) {
    setFileMenuVisible(visible);
  }

  // 表单 form 不显示演示、历史、分享按钮
  const isForm = fileDetail.type === 'form';

  return (
    <div className={classNames(css.header, 'editor-header')}>
      <div className={css.leftSection}>
        <Tooltip classNames={{ root: 'tooltip-offset-y' }} placement="bottom" title={backButtonTipText}>
          <div className={classNames(css.btn, css.arrowRightBtn)} onClick={() => handleMenuItemClick({ key: 'back' })}>
            <LeftOutlined />
          </div>
        </Tooltip>
        <Tooltip classNames={{ root: 'tooltip-offset-y' }} placement="bottom" title={backToButtonTipText}>
          <Popover
            arrow={false}
            classNames={{ root: 'popover-offset-y' }}
            content={<BackToPopover recentFiles={recentFiles} onItemClick={handleMenuItemClick} />}
            placement="bottomLeft"
            trigger="click"
            zIndex={1000}
          >
            <div className={classNames(css.btn, css.arrowDownBtn)}>
              <CaretDownOutlined />
            </div>
          </Popover>
        </Tooltip>
        <Tooltip classNames={{ root: 'tooltip-offset-y' }} placement="bottom" title={createButtonTipText}>
          <Popover
            arrow={false}
            classNames={{ root: 'popover-offset-y' }}
            content={<AddNewPopover onItemClick={handleMenuItemClick} onOpenInNewTab={handleMenuItemOpenInNewTab} />}
            placement="bottomLeft"
            trigger="click"
            zIndex={1000}
          >
            <div className={classNames(css.btn, css.plusBtn)}>
              <PlusOutlined />
            </div>
          </Popover>
        </Tooltip>
        {/* 标题输入框 */}
        <Tooltip classNames={{ root: 'tooltip-offset-y' }} placement="bottom" title={title}>
          <Input
            ref={inputRef}
            className={css.ipt}
            placeholder={inputPlaceholder}
            style={{ width: titleWidth || 'auto' }}
            value={title}
            onBlur={handleBlur}
            onChange={(e) => handleChange(e)}
            onFocus={handleFocus}
            onPressEnter={handlePressEnter}
          />
        </Tooltip>
        <span ref={spanRef} className={css.hiddenMeasure}>
          {title}
        </span>

        <Tooltip classNames={{ root: 'tooltip-offset-y' }} placement="bottom" title={isStar ? unfavorite : favorite}>
          <div className={css.favoriteBtn} onClick={toggleStar}>
            <CancelStarIcon className={classNames(isStar ? css.hidden : css.show, css.cancelStarIcon)} />
            <img className={isStar ? css.show : css.hidden} src={starIcon} width={16} />
          </div>
        </Tooltip>

        {/* 保存状态 */}
        <SaveStatus fileType={fileDetail.type} />
      </div>

      <div className={css.rightSection}>
        <AvatarGroup avatars={collaborators} />
        {/* 历史 */}
        {!isForm && (
          <Tooltip classNames={{ root: 'tooltip-offset-y' }} placement="bottom" title={historyButtonText}>
            <div
              className={classNames(css.btn, css.textBtn, css.historyBtn)}
              onClick={() => handleMenuItemClick({ key: 'viewHistory' })}
            >
              <HistoryIcon />
            </div>
          </Tooltip>
        )}

        {/* 演示 */}
        {!isForm && (
          <Tooltip classNames={{ root: 'tooltip-offset-y' }} placement="bottom" title={demoButtonText}>
            <div
              className={classNames(css.btn, css.textBtn, css.demoBtn)}
              onClick={() => handleMenuItemClick({ key: 'startDemo' })}
            >
              <DemoIcon />
            </div>
          </Tooltip>
        )}
        {/* 分享 协作*/}
        {!isForm && (
          <Tooltip
            classNames={{ root: 'tooltip-offset-y' }}
            placement="bottom"
            title={shareButtonText + teamButtonText}
          >
            <div className={classNames(css.btn, css.textBtn, css.shareBtn)} onClick={() => setShareVisible(true)}>
              {shareButtonText + teamButtonText}
            </div>
          </Tooltip>
        )}

        {/* 文件菜单 */}
        <Tooltip classNames={{ root: 'tooltip-offset-y' }} placement="bottom" title={fileMenuButtonTipText}>
          <Popover
            arrow={false}
            classNames={{ root: 'popover-offset-y' }}
            content={
              <FileMenuPopover
                fileType={fileDetail.type}
                isAdmin={fileDetail.isAdmin || fileDetail.isFileAdmin}
                isFavorite={isStar}
                role={fileDetail.role}
                onItemClick={handleMenuItemClick}
                onOpenInNewTab={handleMenuItemOpenInNewTab}
              />
            }
            destroyTooltipOnHide={true}
            open={fileMenuVisible}
            placement="bottomLeft"
            trigger="click"
            zIndex={1000}
            onOpenChange={fileMenuOpenChange}
          >
            <div className={classNames(css.btn, css.moreBtn)}>
              <MoreIcon height={14.5} width={14.5} />
            </div>
          </Popover>
        </Tooltip>
      </div>
      <CollaborationShare guid={guid} visible={shareVisible} onCancel={() => setShareVisible(false)} />
      {contextHolder}
    </div>
  );
}
