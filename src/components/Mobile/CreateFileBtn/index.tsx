import { Button, Popup } from 'antd-mobile';
import { useState } from 'react';

import addFileBtn from '@/assets/images/svg/addFileBtn.svg';
import { FILE_LIST_H5 } from '@/constants/fileList.config';
import { fm } from '@/modules/Locale';

import FileMenuList from './components/FileMenuList';
import styles from './index.less';

export default () => {
  const [visible, setVisible] = useState(false);
  const i18n_cancel = fm('SiderMenu.cancel');
  return (
    <div>
      <img
        className={styles.fixedButton}
        src={addFileBtn}
        onClick={() => {
          setVisible(true);
        }}
      />

      <Popup
        visible={visible}
        onMaskClick={() => {
          setVisible(false);
        }}
      >
        <FileMenuList config={FILE_LIST_H5} />
        <Button block className={styles.cancelBtn} onClick={() => setVisible(false)}>
          {i18n_cancel}
        </Button>
      </Popup>
    </div>
  );
};
