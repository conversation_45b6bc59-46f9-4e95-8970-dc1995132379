.menuGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); // 一行三个等宽
  // tips: 在grid布局中gap兼容性优于flex;
  row-gap: 20px;
  padding: 10px;
  box-sizing: border-box;
}

.menuItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 5px;
}

.menuIcon {
  @size: 32px;

  width: @size;
  height: @size;
  object-fit: contain;
  margin-bottom: 4px;
}

.menuTitle {
  font-size: 14px;
  color: var(--theme-text-color-default);
  text-align: center;
  word-break: break-word;
}
