import type { FileItem } from '@/constants/fileList.config';
import { useCreateFile } from '@/hooks/useCreateFile';

import CreateFileModal from './CreateFileModal';
import styles from './FileMenuList.less';

interface FileMenuListProps {
  config: FileItem[];
  pid?: string;
  onClose?: () => void;
}

const FileMenuList = ({ config, pid, onClose }: FileMenuListProps) => {
  // todo: 处理 parentGuid; 暂写死;
  const parentGuid = pid || 'Desktop';

  const { createFileForH5 } = useCreateFile(parentGuid);

  const handleClickMenuItem = (item: FileItem) => {
    if (item.needModal) {
      return CreateFileModal.show({
        title: item.title,
        onConfirm: (filename) => {
          createFileForH5(item.value, filename);
        },
        onClose: () => onClose?.(),
      });
    }

    createFileForH5(item.value, '');
  };

  return (
    <div className={styles.menuGrid}>
      {config.map((item) => (
        <div key={item.value} className={styles.menuItem} onClick={() => handleClickMenuItem(item)}>
          <img className={styles.menuIcon} src={item.src} />
          <div className={styles.menuTitle}>{item.title}</div>
        </div>
      ))}
    </div>
  );
};

export default FileMenuList;
