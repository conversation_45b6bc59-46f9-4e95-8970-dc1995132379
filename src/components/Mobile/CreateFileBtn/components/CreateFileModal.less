@radius-sm: 4px;

.createFileModal {
  :global {
    .adm-modal-body {
      padding: 0;

      .adm-modal-content {
        padding: 0;
      }

      .adm-input-element {
        font-size: 14px;
        color: var(--theme-basic-color-primary);

        &::placeholder {
          color: var(--theme-basic-color-black);
        }
      }

      .adm-modal-footer {
        display: none;
      }
    }
  }

  .contentLayout {
    padding: 20px;

    .modalTitle {
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
      text-align: center;
      margin-bottom: 16px;
    }

    .input {
      border-radius: @radius-sm;
      border: 1px solid var(--theme-input-color-border);
      background-color: var(--theme-input-color-bg);
      padding: 6px 10px;
      box-sizing: border-box;
      height: 32px;
    }
  }

  .footerBtnLayout {
    display: flex;

    .btn {
      flex-grow: 1;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-style: normal;
      color: var(--theme-basic-color-primary);
      border-top: 1px solid var(--theme-separator-color-lighter);

      &.btnConfirm {
        color: var(--theme-basic-color-guidance);
      }

      &.disabled {
        color: var(--theme-text-color-disabled);
      }
    }

    .divider {
      width: 1px;
      background-color: var(--theme-separator-color-lighter);
    }
  }
}
