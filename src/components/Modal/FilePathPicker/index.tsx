import { message, Modal } from 'antd';
import { useState } from 'react';

import * as fileApi from '@/api/File';
import { to } from '@/api/Request';
import { fm2 } from '@/modules/Locale';

import { Content } from './Content';
import { SideMenu } from './SideMenu';
import css from './style.less';

export interface Options {
  /** 目标文件id */
  locationGuid?: string;
  title?: string;
  onOk?: () => void;
  onCancel?: () => void;
  type?: 'create' | 'move';
  source: {
    /** 待操作文件 name */
    name: string;
    /** 待操作文件 guid */
    fileGuid: string;
    isAdmin?: boolean;
    role?: string;
  }[];
}

export const FilePathPicker = (options: Options) => {
  const { locationGuid = 'desktop', onOk, type = 'create', source } = options;
  const _filename =
    source.length > 1
      ? `${fm2('FilePathPicker.originalName')} ${fm2('FilePathPicker.copy')}`
      : `${source[0]['name']} ${fm2('FilePathPicker.copy')}`;

  let modalInstance: any;

  function ModalContent() {
    const [curLocationGuid, setCurLocationGuid] = useState(locationGuid);
    const [menuId, setMenuId] = useState<string>('desktop');

    // 创建副本
    async function createDuplicate(locationGuid: string, folderName: string) {
      const params = {
        files: source.map((item) => {
          return {
            guid: item.fileGuid,
            name: `${item.name} ${fm2('FilePathPicker.copy')}`,
          };
        }),
        folder: locationGuid,
      };
      const [, res] = await to(fileApi.duplicate(params));
      if (res?.status !== 200) return message.error(res?.data?.msg);

      message.success(fm2('FilePathPicker.createSuccess', { folderName }));
      onOk?.();
    }

    // 移动到
    async function moveFile(locationGuid: string, folderName: string) {
      const params = {
        entries: source.map((item) => {
          return {
            to: locationGuid,
            fileGuid: item.fileGuid,
          };
        }),
      };
      const [, res] = await to(fileApi.move(params));
      if (res?.status !== 204) return message.error(res?.data?.msg);

      message.success(fm2('FilePathPicker.moveSuccess', { folderName }));
      onOk?.();
    }

    return (
      <div className={css.modalContent}>
        <SideMenu curMenuId={menuId} type={type} onSideMenuChange={setMenuId} />
        <Content
          filename={_filename}
          isAdmin={source[0].isAdmin}
          locationGuid={locationGuid}
          role={source[0].role}
          sideMenuId={menuId}
          type={type}
          onCancel={() => {
            modalInstance.destroy();
          }}
          onLocationChange={(locationGuid) => {
            setCurLocationGuid(locationGuid);
          }}
          onOk={(folderName) => {
            const _locationGuid = curLocationGuid === 'desktop' ? 'Desktop' : curLocationGuid;
            const _folderName = curLocationGuid === 'desktop' ? fm2('FilePathPicker.desktop') : folderName;
            if (type === 'create') {
              createDuplicate(_locationGuid, _folderName);
            } else {
              moveFile(_locationGuid, _folderName);
            }
            modalInstance.destroy();
          }}
          onSideMenuChange={setMenuId}
        />
      </div>
    );
  }

  modalInstance = Modal.info({
    width: 893,
    icon: null,
    footer: null,
    centered: true,
    closable: false,
    className: css.filePathPicker,
    content: <ModalContent />,
  });
};
