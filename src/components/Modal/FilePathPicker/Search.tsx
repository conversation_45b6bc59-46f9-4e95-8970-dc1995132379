import { LoadingOutlined } from '@ant-design/icons';
import type { InputRef } from 'antd';
import { Input } from 'antd';
import { useEffect, useRef, useState } from 'react';

import * as fileApi from '@/api/File';
import { to } from '@/api/Request';
import folderIcon from '@/assets/images/fileIcon/<EMAIL>';
import { ReactComponent as SpaceIcon } from '@/assets/images/sidepanel/space.svg';
import { ReactComponent as CloseIcon } from '@/assets/images/svg/close-24.svg';
import { ReactComponent as SearchIcon } from '@/assets/images/svg/search-18.svg';
import { fm2 } from '@/modules/Locale';

import type { FolderItem } from './FolderManager';
import css from './style.less';

interface SearchProps {
  onItemClick: (folder: FolderItem) => void;
}

export function Search({ onItemClick }: SearchProps) {
  // 搜索相关状态
  const [showInput, setShowInput] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [searchResults, setSearchResults] = useState<FolderItem[]>([]);
  const [loading, setLoading] = useState(false);
  const inputRef = useRef<InputRef>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const searchTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 使用防抖进行搜索
  useEffect(() => {
    if (!searchText.trim()) {
      setSearchResults([]);
      return;
    }

    setLoading(true);

    // 清除之前的定时器
    if (searchTimerRef.current) {
      clearTimeout(searchTimerRef.current);
    }

    // 如果有正在进行的请求，取消它
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }

    // 创建新的定时器进行防抖
    searchTimerRef.current = setTimeout(async () => {
      // 创建新的请求控制器
      const controller = new AbortController();
      abortControllerRef.current = controller;

      try {
        // 直接调用API进行搜索
        const [, res] = await to(fileApi.searchFile({ keyword: searchText, size: 100, type: 'folder' }));

        // 如果请求已被取消，则不处理结果
        if (controller.signal.aborted) return;
        if (res?.status !== 200) {
          setSearchResults([]);
          return;
        }

        const results = res.data.results || [];
        const list = results
          .filter((item: any) => item.source.type === 'folder')
          .map((item: any) => ({
            id: item.source.guid,
            name: item.source.name,
            type: 'folder',
            sourceMenuId: item.source.isSpace ? 'space' : item.source.starred ? 'favorites' : 'desktop',
          }))
          .sort((a: any, b: any) => {
            if (a.sourceMenuId === 'space') return 1;
            if (b.sourceMenuId === 'space') return -1;
            return 0;
          });
        setSearchResults(list);
      } catch (error: any) {
        // 请求已被取消，不做任何处理
        if (controller.signal.aborted) return;

        console.error('搜索出错:', error);
        setSearchResults([]);
      } finally {
        // 请求已被取消，不更新loading状态
        if (!controller.signal.aborted) {
          setLoading(false);
        }
      }
    }, 300);

    return () => {
      // 组件卸载或依赖变化时清理
      if (searchTimerRef.current) {
        clearTimeout(searchTimerRef.current);
      }

      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [searchText]);

  // 点击搜索图标，显示搜索输入框
  function searchIconClick() {
    setShowInput(true);
    // 使用setTimeout确保DOM已更新后再聚焦
    setTimeout(() => {
      inputRef.current?.focus();
    }, 0);
  }

  // 处理搜索输入框失去焦点
  function inputBlur() {
    if (!searchText) {
      setShowInput(false);
    }
  }

  // 处理搜索框内容变化
  function inputChange(e: React.ChangeEvent<HTMLInputElement>) {
    setSearchText(e.target.value);
  }

  // 清除搜索框内容
  function inputClear() {
    setSearchText('');
    setSearchResults([]);
  }

  function itemClick(folder: FolderItem) {
    setShowInput(false);
    onItemClick?.(folder);
  }

  // 渲染搜索结果列表
  function renderResults() {
    return (
      <ul className={css.searchResults}>
        {searchResults.map((folder) => (
          <li key={folder.id} onClick={() => itemClick(folder)}>
            {folder.sourceMenuId === 'space' ? (
              <SpaceIcon className={css.spaceIcon} />
            ) : (
              <img className={css.folderIcon} src={folderIcon} />
            )}
            <span className={css.fileName}>{folder.name}</span>
          </li>
        ))}
      </ul>
    );
  }

  // 渲染搜索结果容器
  function renderSearchResults() {
    let content;

    if (loading) {
      content = (
        <div className={css.loadingContainer}>
          <LoadingOutlined />
        </div>
      );
    } else if (searchResults.length > 0) {
      content = renderResults();
    } else {
      content = <div className={css.noResults}>{fm2('FilePathPicker.noSearchResult')}</div>;
    }

    return <div className={css.resultContainer}>{content}</div>;
  }

  return (
    <div className={css.searchContainer}>
      {showInput ? (
        <div className={css.searchWrapper}>
          <Input
            ref={inputRef}
            placeholder={fm2('FilePathPicker.searchPlaceholder')}
            prefix={<SearchIcon className={css.inputSearchIcon} />}
            style={{ width: '100%' }}
            suffix={<CloseIcon className={css.inputCloseIcon} onClick={inputClear} />}
            value={searchText}
            onBlur={inputBlur}
            onChange={inputChange}
          />

          {searchText && renderSearchResults()}
        </div>
      ) : (
        <SearchIcon className={css.searchIcon} onClick={searchIconClick} />
      )}
    </div>
  );
}
