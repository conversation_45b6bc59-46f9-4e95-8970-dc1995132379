import { Button, message } from 'antd';
import { useState } from 'react';

import * as EnterpriseApi from '@/api/EnterpriseSettings';
import { uploadAvatarToken } from '@/api/Profile';
import { to } from '@/api/Request';
import emptyImg from '@/assets/images/common/<EMAIL>';
import { ReactComponent as UploadIcon } from '@/assets/images/svg/cloudUp.svg';
import { fm2 } from '@/modules/Locale';

import css from './style.less';

interface Props {
  onOk: (url: string) => void;
  type: 'upload' | 'modify';
  logoUrl?: string;
}

export function ModalContent(props: Props) {
  const { onOk, type, logoUrl: propLogoUrl } = props;

  const [file, setFile] = useState<File | null>(null);
  const [logoUrl, setLogoUrl] = useState('');
  const [loading, setLoading] = useState(false);
  async function handleUpload(file: File) {
    setLoading(true);
    const formData = new FormData();
    formData.append('avatar', file);
    const params = [
      {
        bucket: 'avatar',
        filename: file.name,
        fileSize: file.size,
      },
    ];

    const [, res] = await to(uploadAvatarToken(params));
    if (res?.status === 200) {
      const data = res.data.datas[0];
      const params = {
        avatar: data.images,
      };
      const [, res2] = await to(EnterpriseApi.updateInfo(params));
      if (res2?.status === 200) {
        onOk(res2.data.avatar);
      }
    }
    setLoading(false);
  }

  function upload() {
    // 创建文件选择器
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.jpg,.jpeg,.png,.gif';
    fileInput.style.display = 'none';
    // 监听文件选择事件
    fileInput.onchange = (e: Event) => {
      const files = (e.target as HTMLInputElement).files;
      if (!files?.length) return;
      const file = Array.from(files)[0];
      if (file.size > 2 * 1024 * 1024) {
        message.error(fm2('Enterprise.uploadLogoRule'));
        return;
      }
      setFile(file);

      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoUrl(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      fileInput.onchange = null; // 清理事件监听器
    };

    // 触发文件选择器点击
    document.body.appendChild(fileInput);
    fileInput.click();
    // 清理DOM和事件监听器
    setTimeout(() => {
      document.body.removeChild(fileInput);
    }, 100);
  }

  function handleOk() {
    if (file) handleUpload(file);
  }

  return (
    <div className={css.container}>
      <div className={css.header}>
        <div className={css.title}>{type === 'upload' ? fm2('Enterprise.upload') : fm2('Enterprise.modify')}logo</div>
        <Button className={css.btn} icon={<UploadIcon />} onClick={upload}>
          {fm2('Enterprise.upload')}logo
        </Button>
      </div>
      <div className={css.content}>
        <div className={css.logoWrap}>
          <img src={logoUrl || propLogoUrl || emptyImg} />
        </div>
        <div className={css.tip}>{fm2('Enterprise.uploadLogoTip')}</div>
      </div>
      <div className={css.footer}>
        <Button color="default" loading={loading} variant="solid" onClick={handleOk}>
          {fm2('Common.confirm')}
        </Button>
      </div>
    </div>
  );
}
