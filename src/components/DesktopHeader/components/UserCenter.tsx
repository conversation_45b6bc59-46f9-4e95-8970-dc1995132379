import { BankOutlined, CheckOutlined, GlobalOutlined, PoweroffOutlined, SettingFilled } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Avatar, Card, Flex, Menu, Popover, Progress, Space, Tooltip } from 'antd';
import { useMemo, useState } from 'react';
import { history } from 'umi';

import { quota } from '@/api/Me';
import defaultAvatar from '@/assets/images/editor/default-avatar-moke.webp';
import { setLocale } from '@/hooks/Locale';
import { getStoredLocale, useFormatMessage } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';
import { formatFileSizeHuman } from '@/utils/file';

import styles from '../index.less';

type MenuItem = Required<MenuProps>['items'][number];

type PersonalDiskVolume = {
  isLimited: boolean;
  quota: number;
  remaining: number;
  used: number;
};

interface Props {
  size?: number;
  trigger?: ('click' | 'hover')[];
}

const UserCenter = ({ size = 24, trigger = ['click'] }: Props) => {
  const { avatar, name, email, team } = useMeStore((state) => state.me);

  const userAvatar = avatar || defaultAvatar;

  const [currentLocale, setCurrentLocal] = useState(getStoredLocale() || 'zh-CN');

  const [personalDiskVolume, setPersonalDiskVolume] = useState<PersonalDiskVolume>({
    isLimited: false,
    /** 配额 */
    quota: 0,
    /** 剩余 */
    remaining: 0,
    used: 0,
  });

  const items: MenuItem[] = [
    {
      key: '1',
      label: useFormatMessage('UserCenter.settings'),
      icon: <SettingFilled />,
      onClick: () => {
        const href = history.createHref({ pathname: '/profile/accountinfo' });
        window.open(href, '_blank');
      },
    },
    {
      key: '2',
      label: useFormatMessage('UserCenter.myBusiness'),
      icon: <BankOutlined />,
      onClick: () => {
        const href = history.createHref({ pathname: '/members' });
        window.open(href, '_blank');
      },
    },
    {
      type: 'divider',
    },
    {
      key: 'switchLanguages',
      label: useFormatMessage('UserCenter.switchLanguages'),
      icon: <GlobalOutlined />,
      popupOffset: [10, 0],
      children: [
        {
          key: 'zh-CN',
          label: (
            <Space>
              <>简体中文</>
              {currentLocale === 'zh-CN' && <CheckOutlined />}
            </Space>
          ),
          icon: <span />,
          onClick: () => {
            setLocale('zh-CN');
            setCurrentLocal('zh-CN');
          },
        },
        {
          key: 'en-US',
          label: (
            <Space>
              <>English</>
              {currentLocale === 'en-US' && <CheckOutlined />}
            </Space>
          ),
          icon: <span />,
          onClick: () => {
            setLocale('en-US');
            setCurrentLocal('en-US');
          },
        },
      ],
    },
    {
      type: 'divider',
    },
    {
      key: '7',
      label: useFormatMessage('UserCenter.logOut'),
      icon: <PoweroffOutlined />,
      onClick: () => {
        location.href = '/api/v1/auth/logout';
      },
    },
  ];

  const onOpenChange = (open: boolean) => {
    if (!open) return;
    quota().then((res) => {
      if (res.status === 200) {
        setPersonalDiskVolume(res.data?.personalDiskVolume);
      }
    });
  };

  const percent = useMemo(() => {
    return personalDiskVolume.used / personalDiskVolume.quota;
  }, [personalDiskVolume]);

  return (
    <Popover
      content={
        <Card
          className={styles['userCenter']}
          styles={{
            body: {
              padding: '20px 10px 8px',
            },
          }}
        >
          <Space direction="vertical">
            <Space>
              <Avatar size={48} src={userAvatar} />
              <Flex vertical={true}>
                <Tooltip title={name}>
                  <span className={styles['userName']}>{name}</span>
                </Tooltip>

                <Tooltip title={email}>
                  <span className={styles['userEmail']}>{email}</span>
                </Tooltip>

                <Tooltip title={team?.name}>
                  <span className={styles['userEmail']}>{team?.name}</span>
                </Tooltip>
              </Flex>
            </Space>
            <Card size="small">
              <span className={styles['totalEnterpriseCapacity']}>
                {useFormatMessage('UserCenter.totalEnterpriseCapacity')}
              </span>
              <Progress percent={percent} showInfo={false} size="small" type="line" />
              <span className={styles['totalPercent']}>{`${formatFileSizeHuman(
                personalDiskVolume.used,
              )}/${formatFileSizeHuman(personalDiskVolume.quota)}`}</span>
            </Card>
            <Menu items={items} mode="vertical" selectedKeys={[]} />
          </Space>
        </Card>
      }
      placement="bottom"
      styles={{
        body: {
          width: 260,
        },
      }}
      trigger={trigger}
      onOpenChange={onOpenChange}
    >
      <Avatar className={styles.userAvatar} size={size} src={userAvatar} />
    </Popover>
  );
};

export default UserCenter;
