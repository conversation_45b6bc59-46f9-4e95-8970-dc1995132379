.headerContent {
  display: flex;
  box-shadow:
    rgba(0, 0, 0, 6%) 0 1px 1px 0,
    rgba(0, 0, 0, 10%) 0 2px 4px 0;
  z-index: 2;
}

.headerName {
  padding-right: 24px;
  width: 220px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  color: var(--sm-color-gray100);
  font-size: 12px;

  &:hover {
    color: unset;
  }
}

.headerSearch {
  display: flex;
  flex: 1;
  align-items: center;
}

.headerSearchDropdown {
  width: 560px;
  padding: 0;

  :global {
    .ant-select-item-option-grouped {
      padding-inline-start: 10px;
    }
  }
}

.searchContent {
  display: flex;
  flex-direction: column;
}

.searchSelectList {
  display: flex;
}

.searchContentTitle {
  font-size: 10px;
  color: var(--theme-text-color-secondary);
}

.searchSelectLastLegend {
  width: 6px;
  line-height: 24px;
  text-align: center;
  padding: 4px;
}

.searchSelectTarget {
  font-size: 12px;
  color: var(--transparency90);
  line-height: 24px;
  position: relative;

  svg {
    position: absolute;
    top: 0;
    left: 0;
  }

  :global {
    .ant-typography {
      font-size: 12px;
      color: var(--transparency90);
    }
  }

  &:hover,
  span:hover {
    color: var(--theme-link-button-color);
  }

  span {
    padding-left: 20px;
  }
}

.searchSelectRight {
  padding: 0 8px;
  display: flex;
  flex: 1;
  flex-direction: column;
}

.searchSelectHighlight {
  line-height: 24px;
  font-size: 14px;
  font-weight: 500;
  color: var(--theme-basic-color-primary);
}

.searchSelectContent {
  font-size: 12px;
  line-height: 20px;
  color: var(--theme-text-color-secondary);
}

.searchSelectLast {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.searchSelectLastLeft {
  color: var(--theme-text-color-medium);
  line-height: 24px;

  :global {
    .ant-typography {
      font-size: 12px;
      line-height: 20px;
      color: var(--theme-text-color-medium);
    }
  }
}

.searchSelectLastRight {
  color: var(--theme-icon-info-color);
}

.searchSelectLastUpdatedUser {
  color: var(--theme-icon-info-color);
  width: 60px;
  font-size: 12px;
  padding: 0 4px;
}

.usedSelectList {
  display: flex;
  justify-content: space-between;
  line-height: 20px;
  padding: 8px 0;
}

.usedSelectLeft {
  display: flex;
  justify-content: space-between;

  span {
    color: var(--theme-basic-color-primary);
    font-size: 13px;
    font-weight: 400;
    padding-left: 8px;
  }
}

.usedSelectRight {
  display: flex;
  justify-content: space-between;
  font-size: 10px;
  font-weight: 400;
  color: var(--theme-text-color-secondary);
}

.usedSelectDate {
  width: 60px;
  display: contents;
  float: right;
}

.usedSelectUser {
  width: 10px;
  padding: 0 8px;
}

.usedSelectAction {
  width: 20px;
}

.searchNotFoundContent {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.messageContent {
  :global {
    .ant-card-head {
      padding: 8px 10px;
      min-height: 20px;
    }

    .ant-card-body {
      height: calc(100vh - 98px);
      overflow-y: scroll;
      padding: 0;
    }

    .ant-list-header {
      padding: 8px 20px;
      font-size: 10px;
      line-height: 16px;
      color: var(--theme-text-color-disabled);
    }

    .ant-list-item {
      padding: 0;
    }

    .ant-typography {
      margin-bottom: 0;
    }

    .ant-empty {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

.messageTopButton {
  font-size: 10px;
  color: var(--theme-basic-color-primary);
}

.messageName span {
  color: var(--theme-text-color-header);
  font-size: 13px;
  line-height: 20px;
}

.messageType span {
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  line-height: 20px;
}

.messageTarget span {
  color: var(--theme-text-color-header);
  font-size: 13px;
  line-height: 20px;
}

.messageBg {
  padding: 15px 25px;
  cursor: pointer;
}

.messageUnRead {
  background: var(--theme-notice-color-bg);

  .markRead {
    visibility: hidden;
  }

  &:hover {
    background: var(--theme-status-color-bg-guidance);

    .markRead {
      visibility: visible;
    }
  }
}

.messageHeaderClassName {
  padding: 0;
}

.messageCreatedAt {
  color: var(--theme-text-color-secondary);
  font-size: 12px;
  line-height: 20px;
}

.userCenter {
  background-image: var(--user-center-bg);
  background-size: 260px 142px;
  background-repeat: no-repeat;

  :global {
    .ant-menu-light.ant-menu-root.ant-menu-vertical {
      border-inline-end: none;
    }

    .ant-menu {
      .ant-menu-submenu,
      .ant-menu-item {
        padding-left: 8px;
      }
    }
  }
}

.totalEnterpriseCapacity {
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
  color: var(--theme-basic-color-primary);
}

.totalPercent {
  color: var(--theme-text-color-secondary);
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}

.userAvatar {
  cursor: pointer;
}

.userName {
  color: var(--theme-chart-tip-color-text);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  width: 160px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.userEmail {
  color: var(--theme-text-color-medium);
  font-feature-settings:
    'liga' off,
    'clig' off;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-weight: 400;
  line-height: 20px;
}
