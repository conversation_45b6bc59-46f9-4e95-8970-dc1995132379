import { CaretDownOutlined, CheckOutlined, PlusCircleOutlined, UserOutlined } from '@ant-design/icons';
import { Avatar, Button, Collapse, Dropdown, message, Space, Tooltip } from 'antd';
import { useEffect, useState } from 'react';

import {
  deleteAdmin,
  deleteCollaboration,
  deleteCollaborationDepartment,
  deleteDepAdmin,
  getCollaborationList,
  updateCollaboration,
  updateCollaborationDepartment,
  updateParentCollaboration,
} from '@/api/Collaboration';
import listEmpty from '@/assets/images/common/listEmpty.png';
import { ReactComponent as BlueBottom } from '@/assets/images/svg/blueBottom.svg';
import { ReactComponent as BlueRight } from '@/assets/images/svg/blueRight.svg';
import { ReactComponent as CollaborativeArrow } from '@/assets/images/svg/collaborativeArrow.svg';
import { ReactComponent as Organization } from '@/assets/images/svg/organization.svg';
import { ReactComponent as SuperiorOrganization } from '@/assets/images/svg/superiorOrganization.svg';
import deleteConfirm from '@/components/fileList/components/deleteConfirm';
import { fm2 } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

import { itemRole, itemsPrenet } from './components';
interface CollaborationListProps {
  data: any;
  canExit: boolean;
  canManageAdmin: boolean;
  canManageCollaborator: boolean;
  guid: string;
  parentId?: number;
  parentRole: string;
  getUserList: () => void;
  CollaborationAdmins: any[];
  CollaborationRoles: any[];
  setAddOpen: (open: boolean) => void;
  setAddAdminsOrRoles: (type: string) => void;
}
export const CollaborationList: React.FC<CollaborationListProps> = ({
  data,
  canExit,
  canManageAdmin,
  canManageCollaborator,
  guid,
  parentId,
  parentRole,
  getUserList,
  CollaborationAdmins,
  CollaborationRoles,
  setAddOpen,
  setAddAdminsOrRoles,
}) => {
  const [parentRoleStatus, setParentRoleStatus] = useState(parentRole);
  const [collapseOpenList, setCollapseOpenList] = useState<string[]>([]);
  const [inheritedUserList, setInheritedUserList] = useState<any[]>([]);
  const meId = useMeStore((state) => state.me.id);
  const getItemName = (itemsObj: any[], item: any) => {
    const foundItem = itemsObj.find((e: any) => e.key === item);
    return foundItem ? foundItem.label : '';
  };
  const dropdownChangePerent = (info: { key: string }) => {
    updateParentCollaboration(guid, { parentRole: info.key }).then((res) => {
      if (res.status === 200) {
        setParentRoleStatus(res.data.parentRole);
        getCollaborationList(guid, { includeInherited: true, includeAdmin: false, includeSelf: false }).then((res) => {
          setInheritedUserList(res.data.roles);
          getUserList();
          message.success(fm2('ShareCollaboration.modifySuccess'));
        });
      }
    });
  };
  const handleCollapse = (key: string) => {
    if (!collapseOpenList.includes('superior')) {
      getCollaborationList(guid, { includeInherited: true, includeAdmin: false, includeSelf: false }).then((res) => {
        setInheritedUserList(res.data.roles);
      });
    }
    setCollapseOpenList(() => {
      if (collapseOpenList.includes(key)) {
        return collapseOpenList.filter((item) => item !== key);
      } else {
        return [...collapseOpenList, key];
      }
    });
  };
  const dropdownChange = (info: { key: string }, item?: any) => {
    if (info.key === 'remove') {
      if (item.departmentId) {
        deleteCollaborationDepartment(guid, item.id).then((resdep) => {
          if (resdep.status === 204) {
            getUserList();
            message.success(fm2('ShareCollaboration.deleteSuccess'));
          }
        });
      } else {
        deleteCollaboration(guid, item.id).then((res) => {
          if (res.status === 204) {
            getUserList();
            message.success(fm2('ShareCollaboration.modifySuccess'));
          }
        });
      }
    } else {
      if (item.departmentId) {
        updateCollaborationDepartment(guid, item.id, { role: info.key, needNotice: true }).then((resdep) => {
          if (resdep.status === 204) {
            getUserList();
            message.success(fm2('ShareCollaboration.modifySuccess'));
          }
        });
      } else {
        updateCollaboration(guid, item.id, { role: info.key }).then((res) => {
          if (res.status === 200) {
            getUserList();
            message.success(fm2('ShareCollaboration.modifySuccess'));
          }
        });
      }
    }
  };
  const deleteAdminDep = (item: any) => {
    deleteConfirm({
      i18nText: {
        title: fm2('ShareCollaboration.confirmRemoveCollaborator'),
        content: fm2('ShareCollaboration.removeCollaborator', { name: item.name }),
        okText: fm2('ShareCollaboration.confirmRemove'),
        cancelText: fm2('ShareCollaboration.cancel'),
        success: fm2('ShareCollaboration.success'),
        error: fm2('ShareCollaboration.failed'),
      },
      data: { guid, id: item.id, name: item.name },
      api: () => (item.departmentId ? deleteDepAdmin(guid, item.id) : deleteAdmin(guid, item.id)),
      callback: () => getUserList(),
    });
  };
  const editCollaboration = (key: string, item?: any) => {
    if (meId === item.id) {
      // 判断本人是否可以退出协作
      if (!canExit) {
        return key === 'remove';
      }
    }
  };
  const disabledAdmin = (item: any) => {
    if (!canManageAdmin || CollaborationAdmins.length === 1 || item.isInherited || data?.userId === item.id) {
      return true;
    } else {
      return false;
    }
  };
  const handleEnterAddRoles = () => {
    setAddOpen(true);
    setAddAdminsOrRoles('roles');
  };
  const handleEnterAddAdmins = () => {
    setAddOpen(true);
    setAddAdminsOrRoles('admins');
  };
  useEffect(() => {
    getUserList();
  }, [getUserList]);
  return (
    <div className="collaborationList">
      <div className="listTitle">
        <div>{fm2('ShareCollaboration.coauthor')}</div>
        {canManageCollaborator && (
          <div className="listTitleRight" onClick={handleEnterAddRoles}>
            <PlusCircleOutlined />
            {fm2('ShareCollaboration.addCoauthor')}
          </div>
        )}
      </div>
      <div className="listBox marginBottom16">
        {CollaborationRoles.length > 0 || parentId ? (
          <>
            {parentId && (
              <Collapse
                activeKey={collapseOpenList}
                bordered={false}
                expandIcon={() => {
                  return '';
                }}
                items={[
                  {
                    key: 'superior',
                    label: (
                      <div className="listItem">
                        <div className="itemLeft">
                          <SuperiorOrganization />
                          <span className="superOrg">{fm2('ShareCollaboration.parentCoauthor')}</span>
                          <div className="collapseBtn" onClick={() => handleCollapse('superior')}>
                            {collapseOpenList.includes('superior') ? (
                              <div className="collapseClick">
                                <span>{fm2('ShareCollaboration.collapse')}</span>
                                <BlueBottom />
                              </div>
                            ) : (
                              <div className="collapseClick">
                                <span>{fm2('ShareCollaboration.expand')}</span>
                                <BlueRight />
                              </div>
                            )}
                          </div>
                        </div>
                        <Dropdown
                          menu={{
                            items: itemsPrenet.map((roleItem) => ({
                              key: roleItem.key,
                              label: (
                                <div className="dropdownItem">
                                  <div>{roleItem.label}</div>
                                  {parentRoleStatus === roleItem.key && <CheckOutlined />}
                                </div>
                              ),
                            })),
                            onClick: (info) => dropdownChangePerent(info),
                          }}
                          placement="bottomRight"
                          trigger={['click']}
                        >
                          <Space className="spaceCollaboration">
                            <Button
                              disabled={!canManageCollaborator}
                              icon={<CollaborativeArrow />}
                              iconPosition="end"
                              size="small"
                              type="text"
                            >
                              {getItemName(itemsPrenet, parentRoleStatus)}
                            </Button>
                          </Space>
                        </Dropdown>
                      </div>
                    ),
                    children: (
                      <div>
                        {inheritedUserList.length > 0 ? (
                          inheritedUserList.map((item) => (
                            <div key={item.id} className="listItem">
                              <div className="itemLeft">
                                {item.departmentId ? (
                                  <>
                                    <Organization />
                                    <div className="ellipsis">{item.name}</div>
                                  </>
                                ) : (
                                  <>
                                    <Avatar icon={<UserOutlined />} size={28} src={item.avatar} />
                                    <div className="itemRight">
                                      <div className="itemName">{item.name}</div>
                                      <Tooltip placement="top" title={item.email}>
                                        <div className="emailText">{item.email}</div>
                                      </Tooltip>
                                    </div>
                                  </>
                                )}
                              </div>
                              <Space className="spaceCollaboration">
                                <span className="noClick">{getItemName(itemsPrenet, item.role)}</span>
                              </Space>
                            </div>
                          ))
                        ) : (
                          <div className="noDataInherited">{fm2('ShareCollaboration.noParentCollaborator')}</div>
                        )}
                      </div>
                    ),
                  },
                ]}
              />
            )}
            {CollaborationRoles.map((item) => {
              return (
                <div key={item.id} className="listItem">
                  <div className="itemLeft">
                    {item.departmentId ? (
                      <>
                        <Organization />
                        <div className="ellipsis">{item.name}</div>
                      </>
                    ) : (
                      <>
                        <Avatar icon={<UserOutlined />} size={28} src={item.avatar} />
                        <div className="itemRight">
                          <div className="itemName">{item.name}</div>
                          <Tooltip placement="top" title={item.email}>
                            <div className="emailText">{item.email}</div>
                          </Tooltip>
                        </div>
                      </>
                    )}
                  </div>
                  <Dropdown
                    menu={{
                      items: itemRole.map((roleItem) => ({
                        key: roleItem.key,
                        danger: roleItem.danger,
                        label: (
                          <div className="dropdownItem">
                            <div>{roleItem.label}</div>
                            {item.role === roleItem.key && <CheckOutlined />}
                          </div>
                        ),
                        disabled: editCollaboration(roleItem.key, item),
                      })),
                      onClick: (info) => dropdownChange(info, item),
                    }}
                    placement="bottomRight"
                    trigger={['click']}
                  >
                    <Space className="spaceCollaboration">
                      <Button
                        disabled={!canManageCollaborator}
                        icon={<CaretDownOutlined />}
                        iconPosition="end"
                        size="small"
                        type="text"
                      >
                        {getItemName(itemRole, item.role)}
                      </Button>
                    </Space>
                  </Dropdown>
                </div>
              );
            })}
          </>
        ) : (
          <div className="listEmpty">
            <img alt="" src={listEmpty} />
          </div>
        )}
      </div>
      <div className="listTitle">
        <div>{fm2('ShareCollaboration.admin')}</div>
        {canManageAdmin && (
          <div className="listTitleRight" onClick={handleEnterAddAdmins}>
            <PlusCircleOutlined />
            {fm2('ShareCollaboration.addManager')}
          </div>
        )}
      </div>
      <div className="listBox">
        {CollaborationAdmins.map((item) => {
          return (
            <div key={item.id} className="listItem">
              <div className="itemLeft">
                {item.departmentId ? (
                  <>
                    <Organization />
                    <div className="ellipsis">{item.name}</div>
                  </>
                ) : (
                  <>
                    <Avatar icon={<UserOutlined />} size={28} src={item.avatar} />
                    <div className="itemRight">
                      <div className="itemName">{item.name}</div>
                      <Tooltip placement="top" title={item.email}>
                        <div className="emailText">{item.email}</div>
                      </Tooltip>
                    </div>
                  </>
                )}
              </div>
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'merger',
                      label: (
                        <div className="dropdownItem">
                          <div>{fm2('ShareCollaboration.admin')}</div>
                          <CheckOutlined />
                        </div>
                      ),
                    },
                    {
                      key: 'removeMerger',
                      danger: true,
                      label: <div>{fm2('ShareCollaboration.removeManager')}</div>,
                      onClick: () => deleteAdminDep(item),
                    },
                  ],
                }}
                placement="bottomRight"
                trigger={['click']}
              >
                <Space className="spaceCollaboration">
                  <Button
                    disabled={disabledAdmin(item)}
                    icon={<CaretDownOutlined />}
                    iconPosition="end"
                    size="small"
                    type="text"
                  >
                    {fm2('ShareCollaboration.admin')}
                  </Button>
                </Space>
              </Dropdown>
            </div>
          );
        })}
      </div>
    </div>
  );
};
