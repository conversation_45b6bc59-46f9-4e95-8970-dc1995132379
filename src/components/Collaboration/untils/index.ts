import {
  getCollaborationDetail,
  getCollaborationList,
  SharePasswordStatus,
  updateExpireTime,
  updateShareStatus,
} from '@/api/Collaboration';

// 获取团队空间/文件夹/文件详情
export const getCollaborationDataInfo = async (guid: string) => {
  try {
    const res = await getCollaborationDetail(guid);
    const data = res.data;
    let shareUrl = '';
    let currentGuid = null;
    if (data?.isShortcut) {
      shareUrl = data.shortcutSource?.url || '';
      currentGuid = data.shortcutSource?.guid || null;
    } else {
      shareUrl = data.url || '';
      currentGuid = data.guid || null;
    }
    const copyUrl = `${new URL(shareUrl, window.location.href).href}/《${data.name}》`;
    return {
      data,
      copyUrl,
      currentGuid,
    };
  } catch (error) {
    console.error(error);
    return null;
  }
};
//获取协作者和管理者列表
export const fetchCollaborationInfo = async (guid: string) => {
  try {
    const result = await getCollaborationList(guid, {
      includeInherited: false,
      includeAdmin: true,
    });

    return {
      admins: result.data.admins,
      roles: result.data.roles,
    };
  } catch (error) {
    return { admins: [], roles: [] };
  }
};

// 分享开关的关闭和开启
export const handleUpdateShareStatus = async (guid: string, shareMode: string) => {
  await updateShareStatus(guid, { shareMode });
};
//获取或者更新密码
export const updateSharePasswordAndGet = async (
  guid: string,
  passwordProtected: boolean,
  reset: boolean,
): Promise<string | null> => {
  try {
    const res = await SharePasswordStatus(guid, { passwordProtected, reset });
    return res.data?.password || null;
  } catch (error) {
    return null;
  }
};
//更新有效期事件
export const updateShareExpire = async (guid: string, value: number): Promise<number> => {
  const res = await updateExpireTime(guid, { shareModeExpireDuration: value ? value * 86400 : undefined });
  return res.data?.shareModeExpiredAt || 0;
};
