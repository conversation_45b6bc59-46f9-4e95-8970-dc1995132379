.collaboratorStruct {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--theme-layout-color-bg-editor);
}

.searchSection {
  padding: 10px 17px;
  background-color: var(--theme-basic-color-bg-default);
  border-bottom: 1px solid var(--theme-separator-color-lighter);
  border-top: 1px solid var(--theme-separator-color-lighter);
  margin-top: 17px;
  display: flex;
  align-items: center;
  color: var(--theme-text-color-secondary);
}

.searchIcon {
  width: 20px;
  height: 20px;
  text-align: center;
  justify-content: center;
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.searchInput {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 14px;
  color: var(--theme-text-color-default);

  &::placeholder {
    color: var(--theme-text-color-secondary);
  }
}

.treeContainer {
  flex: 1;
  overflow-y: auto;

  :global(.adm-collapse) {
    background: transparent;
  }

  :global(.adm-collapse-item) {
    background: var(--theme-basic-color-bg-default);
    border-bottom: 1px solid var(--theme-separator-color-lighter);

    &:last-child {
      border-bottom: none;
    }
  }

  :global(.adm-collapse-header) {
    padding: 0;
    background: transparent;
  }

  :global(.adm-collapse-content) {
    padding: 0;
    background: transparent;
  }

  :global(.adm-collapse-content-box) {
    padding: 0;
  }
}
