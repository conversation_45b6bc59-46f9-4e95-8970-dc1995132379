import { Avatar } from 'antd-mobile';
import { useEffect, useMemo, useState } from 'react';

import { getCollaborationList } from '@/api/Collaboration';
import type { CollaboratorsData } from '@/api/Collaboration.type';
import { ReactComponent as ArrowRight } from '@/assets/images/svg/arrowRight.svg';
import { ReactComponent as NoDataIcon } from '@/assets/images/svg/noDataIcon.svg';
import { useDisclosure } from '@/hooks/useDisclosure';
import { fm } from '@/modules/Locale';

import { AddCollaborator } from './AddCollaborator';
import { CollaboratorManagement } from './CollaboratorManagement';
import { CollaboratorManagementPopup } from './CollaboratorManagementPopup';
import { ParentDirectoryCollaboratorList } from './ParentDirectoryCollaboratorList';
import styles from './ShareUserList.less';

function AvatarList({ avatars }: { avatars: CollaboratorsData['roles'] }) {
  return (
    <div className={styles['shareUserList-avatars']}>
      {avatars.slice(0, 5).map((item) => (
        <Avatar key={item.id} src={item.avatar} />
      ))}
    </div>
  );
}

export const PopInfo = {
  index: {
    title: '协作者',
    type: 'index',
  },
  parent: {
    title: '上级目录协作者',
    type: 'parent',
  },
  add: {
    title: '协作者',
    type: 'add',
  },
} as const;

export function ShareUserList({ guid }: { guid: string }) {
  const [data, setData] = useState<CollaboratorsData>();
  const { isOpen, open, close } = useDisclosure(false);
  const [popInfo, setPopInfo] = useState<(typeof PopInfo)[keyof typeof PopInfo]>(PopInfo.index);

  function handleClose(type: keyof typeof PopInfo) {
    setPopInfo(PopInfo[type]);
  }

  function handlePopClose() {
    if (popInfo.type === PopInfo.index.type) {
      close();
    } else {
      setPopInfo(PopInfo.index);
    }
  }

  useEffect(() => {
    if (guid) {
      getCollaborationList(guid, { includeInherited: false, includeAdmin: true }).then((res) => {
        setData(res.data);
      });
    }
  }, [guid]);

  const hasCollaborators = useMemo(() => data?.roles && data.roles.length > 0, [data]);

  return (
    <>
      <div className={styles.shareUserList} onClick={open}>
        <div className={styles['shareUserList-content']}>
          {hasCollaborators ? (
            <AvatarList avatars={data!.roles} />
          ) : (
            <div className={styles['shareUserList-empty']}>
              <NoDataIcon className={styles['shareUserList-empty-icon']} />
              {fm('ShareCollaboration.noRoles')}
            </div>
          )}
        </div>
        <div className={styles['shareUserList-action']}>
          {fm('ShareCollaboration.addRoles')}
          <ArrowRight />
        </div>
      </div>
      <CollaboratorManagementPopup isOpen={isOpen} title={popInfo.title} onClose={handlePopClose}>
        {popInfo.type === PopInfo.index.type && <CollaboratorManagement data={data} showOtherPop={handleClose} />}
        {popInfo.type === PopInfo.parent.type && <ParentDirectoryCollaboratorList data={data!.roles} />}
        {popInfo.type === PopInfo.add.type && <AddCollaborator />}
      </CollaboratorManagementPopup>
    </>
  );
}
