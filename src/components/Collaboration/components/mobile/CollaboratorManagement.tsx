import { Modal } from 'antd-mobile';
import { useState } from 'react';

import type { CollaboratorsData } from '@/api/Collaboration.type';
import { ReactComponent as ArrowRight } from '@/assets/images/svg/arrowRight.svg';
import { ReactComponent as AvatarBlueIcon } from '@/assets/images/svg/avatar-blue.svg';
import { ReactComponent as LinkShareIcon } from '@/assets/images/svg/link-share.svg';
import { ReactComponent as QuestionCircleIcon } from '@/assets/images/svg/question-circle.svg';
import { ReactComponent as Search } from '@/assets/images/svg/search.svg';
import { useDisclosure } from '@/hooks/useDisclosure';
import { fm } from '@/modules/Locale';

import { CollaboratorList } from './CollaboratorItem';
import styles from './CollaboratorManagement.less';
import { PermissionSettings } from './PermissionSettings';
import { PopInfo } from './ShareUserList';

interface CollaboratorManagementProps {
  data?: CollaboratorsData;
  showOtherPop: (type: keyof typeof PopInfo) => void;
}

export function SectionHeader({ title, onClick }: { title: string; onClick?: () => void }) {
  return (
    <div className={styles.sectionHeader}>
      <div className={styles.sectionTitleWithIcon} onClick={onClick}>
        <span className={styles.sectionTitle}>{title}</span>
        <QuestionCircleIcon />
      </div>
    </div>
  );
}

export function CollaboratorManagement({ data, showOtherPop }: CollaboratorManagementProps) {
  // 根据CollaboratorsData结构，admins和roles是分开的
  const administrators = data?.admins || [];
  const collaborators = data?.roles || [];
  const [visible, setVisible] = useState(false);
  const [modalInfo, setModalInfo] = useState({
    title: '',
    content: '',
  });
  const {
    isOpen: permissionSettingsOpen,
    open: openPermissionSettings,
    close: closePermissionSettings,
  } = useDisclosure(false);

  const handleAddFromContacts = () => {
    showOtherPop(PopInfo.add.type);
  };

  const handleShareByLink = () => {
    // TODO: 实现通过链接邀请逻辑
    console.log('通过链接邀请');
  };

  const handleSearch = () => {
    console.log(PopInfo.parent.type);
  };

  const handleParentDirectoryClick = () => {
    showOtherPop('parent');
  };

  const openAdminInfo = () => {
    setModalInfo({
      title: '管理者',
      content: '参与文件管理的成员列表，管理者可以进行移动和删除操作。上级继承的管理者不可被移除',
    });
    setVisible(true);
  };

  const openRolesInfo = () => {
    setModalInfo({
      title: '协作者',
      content: '参与文件协作的成员列表，协作者不能进行移动和删除操作',
    });
    setVisible(true);
  };

  return (
    <>
      {/* 搜索框 */}
      <div className={styles.searchSection} onClick={handleSearch}>
        <div className={styles.searchIcon}>
          <Search />
        </div>
        <span className={styles.searchPlaceholder}>输入姓名/邮箱/手机/部门，搜索协作者</span>
      </div>

      {/* 添加协作者选项 */}
      <div className={styles.addOptions}>
        <div className={styles.addOption} onClick={handleAddFromContacts}>
          <div className={styles.addOptionLeft}>
            <AvatarBlueIcon className={styles.addOptionIcon} />
            <span className={styles.addOptionText}>{fm('ShareCollaboration.addFromContacts')}</span>
          </div>
          <ArrowRight className={styles.addOptionArrow} />
        </div>

        <div className={styles.addOption} onClick={handleShareByLink}>
          <div className={styles.addOptionLeft}>
            <LinkShareIcon className={styles.addOptionIcon} />
            <span className={styles.addOptionText}>{fm('ShareCollaboration.shareByLink')}</span>
          </div>
          <ArrowRight className={styles.addOptionArrow} />
        </div>
      </div>

      <SectionHeader title={fm('ShareCollaboration.administrators')} onClick={openAdminInfo} />

      {/* 管理者部分 */}
      <CollaboratorList data={administrators} />

      <SectionHeader title={fm('ShareCollaboration.collaborators')} onClick={openRolesInfo} />

      {/* 协作者部分 */}
      <CollaboratorList data={collaborators} open={handleParentDirectoryClick} onClick={openPermissionSettings} />

      <Modal
        closeOnAction
        actions={[
          {
            key: 'confirm',
            text: '知道了',
          },
        ]}
        bodyClassName={styles.modal}
        content={
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
            }}
          >
            <div
              style={{
                fontSize: '16px',
                fontWeight: 500,
                color: 'var(--theme-text-color-header)',
                marginBottom: '10px',
              }}
            >
              {modalInfo.title}
            </div>
            <div
              style={{
                fontSize: '14px',
                color: 'var(--theme-text-color-medium)',
                textAlign: 'center',
                lineHeight: '20px',
              }}
            >
              {modalInfo.content}
            </div>
          </div>
        }
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
      />
      <PermissionSettings close={closePermissionSettings} data={collaborators[0]} visible={permissionSettingsOpen} />
    </>
  );
}
