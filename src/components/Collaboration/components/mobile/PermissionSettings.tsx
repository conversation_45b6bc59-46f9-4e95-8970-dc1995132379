import { EyeOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { ActionSheet } from 'antd-mobile';
import { useMemo } from 'react';

import type { CollaboratorsData } from '@/api/Collaboration.type';
import { ReactComponent as CheckIcon } from '@/assets/images/svg/check.svg';
import { ReactComponent as CommentIcon } from '@/assets/images/svg/comment.svg';
import { ReactComponent as EditIcon } from '@/assets/images/svg/edit.svg';
import { ReactComponent as StructIcon } from '@/assets/images/svg/struct.svg';

import styles from '../../CollaborationShareMobile.less';
import {
  CollarboratorCardAvatar,
  CollarboratorCardConent,
  CollarboratorCardLeft,
  CollarboratorCardName,
} from './Collaborator';

function UserInfo({ data }: { data: CollaboratorsData['roles'][number] }) {
  return (
    <CollarboratorCardConent>
      <CollarboratorCardLeft>
        <CollarboratorCardAvatar avatar={data.avatar} />
        <CollarboratorCardName className={styles.userName} email={data.email} name={data.name} />
      </CollarboratorCardLeft>
    </CollarboratorCardConent>
  );
}

function PermissionItem({
  icon,
  text,
  isChecked,
  onClick,
}: {
  onClick?: () => void;
  text: string;
  icon: React.ReactNode;
  isChecked?: boolean;
}) {
  return (
    <div className={styles.permissionItem} onClick={onClick}>
      <div className={styles.permissionItemLeft}>
        <div className={styles.permissionItemIcon}>{icon}</div>
        <div className={styles.permissionItemText}>{text}</div>
      </div>
      {isChecked && <CheckIcon className={styles.checkIcon} />}
    </div>
  );
}

function PermissionList({ onClick }: { onClick: () => void }) {
  return (
    <div>
      <PermissionItem icon={<StructIcon />} text="继承权限" onClick={onClick} />
      <PermissionItem icon={<EyeOutlined />} text="只能阅读" onClick={onClick} />
      <PermissionItem icon={<CommentIcon />} text="只能评论" onClick={onClick} />
      <PermissionItem icon={<EditIcon />} text="可以编辑" onClick={onClick} />
      <PermissionItem icon={<MinusCircleOutlined />} text="没有权限" onClick={onClick} />
    </div>
  );
}

export function PermissionSettings({
  visible,
  close,
  data,
}: {
  visible: boolean;
  close: () => void;
  data: CollaboratorsData['roles'][number];
}) {
  const actions = useMemo(() => {
    return [
      {
        key: 'linkSharingSwitch',
        text: <UserInfo data={data} />,
      },
      {
        key: 'shareSettings',
        text: <PermissionList onClick={close} />,
      },
    ];
  }, [close, data]);

  return (
    <ActionSheet
      actions={actions}
      cancelText="关闭"
      className={styles.collaborationShareMobile}
      visible={visible}
      onClose={close}
    />
  );
}
