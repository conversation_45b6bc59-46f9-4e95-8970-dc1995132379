.collaboratorManagement {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 11.5px;
  border-bottom: 1px solid var(--theme-separator-color-lighter);
  position: relative;
}

.headerLeft {
  width: 24px;
  display: flex;
  justify-content: center;
  color: var(--theme-text-color-default);
}

.backIcon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  text-align: center;
  justify-content: center;
}

.title {
  font-size: 16px;
  font-weight: 500;
  color: var(--theme-text-color-default);
  position: absolute;
  left: 90px;
  right: 90px;
  display: flex;
  justify-content: center;
}

.content {
  background: var(--theme-layout-color-bg-editor);
  flex: 1;
}

.searchSection {
  padding: 10px 17px;
  background-color: var(--theme-basic-color-bg-default);
  border-bottom: 1px solid var(--theme-separator-color-lighter);
  border-top: 1px solid var(--theme-separator-color-lighter);
  margin-top: 17px;
  display: flex;
  align-items: center;
  color: var(--theme-text-color-secondary);
}

.searchIcon {
  width: 20px;
  height: 20px;
  text-align: center;
  justify-content: center;
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.addOptions {
  margin-top: 20px;
  background-color: var(--theme-basic-color-bg-default);
}

.addOption {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  cursor: pointer;
  border-bottom: 1px solid var(--theme-separator-color-lighter);

  &:last-child {
    border-bottom: none;
  }
}

.addOptionLeft {
  display: flex;
  align-items: center;
}

.addOptionIcon {
  width: 32px;
  height: 32px;
}

.addOptionText {
  margin-left: 12px;
  font-size: 14px;
  color: var(--theme-text-color-default);
}

.addOptionArrow {
  width: 16px;
  height: 16px;
}

.section {
  background-color: var(--theme-basic-color-bg-default);
  border-top: 1px solid var(--theme-separator-color-lighter);
}

.sectionHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-top: 1px solid var(--theme-separator-color-lighter);
}

.sectionTitleWithIcon {
  display: flex;
  align-items: center;
}

.sectionTitle {
  font-size: 12px;
  color: var(--theme-text-color-default);
}

.userItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  border-bottom: 1px solid var(--theme-separator-color-lighter);

  &:last-child {
    border-bottom: none;
  }
}

.userInfo {
  display: flex;
  align-items: center;
  flex: 1;
}

.userAvatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.userDetails {
  display: flex;
  flex-direction: column;
  margin-left: 12px;
  color: var(--theme-text-color-default);
}

.userName {
  font-size: 14px;
}

.userEmail {
  font-size: 12px;
}

.userRole {
  font-size: 12px;
  color: var(--theme-text-color-disabled);
}

.emptyState {
  padding: 5px 16px;
  // border-top: 1px solid var(--theme-separator-color-lighter);
  // border-bottom: 1px solid var(--theme-separator-color-lighter);
}

.emptyText {
  font-size: 12px;
  color: var(--theme-text-color-disabled);
}

.modal {
  :global(.adm-modal-content) {
    padding: 0 16px 20px;
    border-bottom: 1px solid var(--theme-separator-color-lighter);
  }

  :global(.adm-modal-footer) {
    padding-top: 11.5px;
  }

  :global(.adm-button:not(.adm-button-default).adm-button-fill-none) {
    color: var(--theme-basic-color-notice);
  }
}

.itemButton {
  color: var(--theme-text-color-guidance);
  font-size: 12px;
  padding: 5px 6px;
  border: 1px solid var(--theme-basic-color-lighter);
  border-radius: 2px;
}

.userRoleIcon {
  margin-left: 6px;
  width: 16px;
  height: 16px;
  justify-content: center;
}

.watchUser {
  font-size: 12px;
  color: var(--theme-text-color-guidance);
}

.addCollaboratorFooter {
  position: fixed;
  bottom: 0;
  left: 0;
}

.addCollaboratorTabs {
  :global(.adm-tabs-tab) {
    font-size: 13px;
    color: var(--theme-text-color-deep);
  }

  :global(.adm-tabs-tab-line) {
    background: var(--theme-chart-tip-color-text);
  }

  :global(.adm-tabs-content) {
    padding: 0;
  }
}
