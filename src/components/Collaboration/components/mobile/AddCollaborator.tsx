import { Checkbox, Tabs } from 'antd-mobile';
import { useEffect, useState } from 'react';

import { getRecentContact } from '@/api/Collaboration';
import type { ContactData } from '@/api/Collaboration.type';
import { ReactComponent as Search } from '@/assets/images/svg/search.svg';

import { CollarboratorCard } from './Collaborator';
import { CollaboratorRoleItem } from './CollaboratorItem';
import styles from './CollaboratorManagement.less';
import { CollaboratorStruct } from './CollaboratorStruct';

export function AddCollaborator() {
  const [data, setData] = useState<ContactData[]>([]);
  function handleSearch() {
    console.log('search');
  }

  function onClick() {
    console.log('click');
  }

  useEffect(() => {
    getRecentContact().then((res) => {
      setData(res.data.results);
    });
  }, []);

  return (
    <>
      {/* 搜索框 */}
      <div className={styles.searchSection} onClick={handleSearch}>
        <div className={styles.searchIcon}>
          <Search />
        </div>
        <span className={styles.searchPlaceholder}>输入姓名/邮箱/手机/部门，搜索协作者</span>
      </div>
      <Tabs className={styles.addCollaboratorTabs}>
        <Tabs.Tab key="recently" title="最近联系人">
          {data.length > 0 && (
            <CollarboratorCard>
              {data.map((item) => (
                <CollaboratorRoleItem
                  key={item.id}
                  showBtn
                  avatar={item.avatar}
                  displayText={'添加'}
                  email={item.email}
                  name={item.name}
                  onClick={onClick}
                />
              ))}
            </CollarboratorCard>
          )}
        </Tabs.Tab>
        <Tabs.Tab key="organization" title="组织架构">
          <CollaboratorStruct />
        </Tabs.Tab>
      </Tabs>
      <div className={styles.addCollaboratorFooter}>
        <Checkbox>添加协作者时，向对方发送通知</Checkbox>
      </div>
    </>
  );
}
