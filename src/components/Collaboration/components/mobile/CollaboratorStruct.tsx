import { Collapse } from 'antd-mobile';
import { useEffect, useState } from 'react';

import { getOrgDepartment, getOrgDepartmentUser } from '@/api/Collaboration';
import type { DepartmentsData, UserInfo } from '@/api/Collaboration.type';

import {
  CollaboratorCardButton,
  CollaboratorCardSpan,
  CollarboratorCardConent,
  CollarboratorCardLeft,
  CollarboratorCardName,
} from './Collaborator';
import styles from './CollaboratorStruct.less';

function StructItem({
  showBtn,
  displayText,
  onClick,
  name,
}: {
  showBtn?: boolean;
  displayText: string;
  name: string;
  onClick?: () => void;
}) {
  return (
    <CollarboratorCardConent>
      <CollarboratorCardLeft>
        <CollarboratorCardName name={name} />
      </CollarboratorCardLeft>
      {showBtn ? (
        <CollaboratorCardButton text={displayText} onClick={onClick} />
      ) : (
        <CollaboratorCardSpan text={displayText} />
      )}
    </CollarboratorCardConent>
  );
}

export function CollaboratorStruct() {
  const [users, setUsers] = useState<UserInfo[]>([]);
  const [depts, setDepts] = useState<DepartmentsData>();

  async function getData(id: string) {
    Promise.all([getOrgDepartmentUser(id, { perPage: 500, page: 1 }), getOrgDepartment(id)]).then(([users, depts]) => {
      setUsers(users.data.users);
      setDepts(depts.data);
    });
  }

  function onClick() {
    console.log(users);

    console.log('click');
  }

  useEffect(() => {
    getData('1');
  }, []);

  return (
    <div className={styles.collaboratorStruct}>
      <Collapse arrowIcon={null} defaultActiveKey={['1']}>
        <Collapse.Panel key="1" title={<StructItem key="1" displayText={'添加'} name={'test'} onClick={onClick} />}>
          {depts?.subdepartments?.map((item) => (
            <Collapse.Panel
              key={`${item.id}`}
              title={<StructItem key={item.id} displayText={'添加'} name={item.name} onClick={onClick} />}
            >
              test
            </Collapse.Panel>
          ))}
        </Collapse.Panel>
      </Collapse>
    </div>
  );
}
