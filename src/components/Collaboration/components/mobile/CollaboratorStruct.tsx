import { SearchOutlined } from '@ant-design/icons';
import { Collapse, Input } from 'antd-mobile';
import { useCallback, useEffect, useState } from 'react';

import { getOrgDepartment, getOrgDepartmentUser } from '@/api/Collaboration';

import {
  CollaboratorCardButton,
  CollarboratorCardAvatar,
  CollarboratorCardConent,
  CollarboratorCardLeft,
  CollarboratorCardName,
} from './Collaborator';
import styles from './CollaboratorStruct.less';

// 树节点数据类型
interface TreeNode {
  id: string;
  name: string;
  type: 'department' | 'user';
  level: number;
  avatar?: string;
  email?: string;
  children?: TreeNode[];
  hasChildren?: boolean;
  isLoaded?: boolean;
}

// 部门项组件
function DepartmentItem({ node, onAddClick }: { node: TreeNode; onAddClick: (node: TreeNode) => void }) {
  return (
    <CollarboratorCardConent>
      <CollarboratorCardLeft>
        <div style={{ paddingLeft: `${node.level * 16}px` }}>
          <CollarboratorCardName name={node.name} />
        </div>
      </CollarboratorCardLeft>
      <CollaboratorCardButton text="添加" onClick={() => onAddClick(node)} />
    </CollarboratorCardConent>
  );
}

// 用户项组件
function UserItem({ node, onAddClick }: { node: TreeNode; onAddClick: (node: TreeNode) => void }) {
  return (
    <CollarboratorCardConent>
      <CollarboratorCardLeft>
        <div style={{ paddingLeft: `${node.level * 16}px`, display: 'flex', alignItems: 'center' }}>
          <CollarboratorCardAvatar avatar={node.avatar || ''} />
          <CollarboratorCardName email={node.email} name={node.name} />
        </div>
      </CollarboratorCardLeft>
      <CollaboratorCardButton text="添加" onClick={() => onAddClick(node)} />
    </CollarboratorCardConent>
  );
}

export function CollaboratorStruct() {
  const [searchValue, setSearchValue] = useState('');
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [expandedKeys] = useState<string[]>(['1']);

  // 加载部门数据
  const loadDepartmentData = useCallback(async (departmentId: string, level: number = 0) => {
    const [usersResponse, departmentsResponse] = await Promise.all([
      getOrgDepartmentUser(departmentId, { perPage: 500, page: 1 }),
      getOrgDepartment(departmentId),
    ]);

    const departments: TreeNode[] = departmentsResponse.data.subdepartments.map((department) => ({
      id: department.id.toString(),
      name: department.name,
      type: 'department' as const,
      level: level + 1,
      hasChildren: true,
      isLoaded: false,
    }));

    const users: TreeNode[] = usersResponse.data.users.map((user) => ({
      id: `user_${user.id}`,
      name: user.name,
      type: 'user' as const,
      level: level + 1,
      avatar: user.avatar,
      email: user.email,
    }));

    return [...departments, ...users];
  }, []);

  // 初始化根部门数据
  useEffect(() => {
    loadDepartmentData('1', 0).then((data) => {
      setTreeData(data);
    });
  }, [loadDepartmentData]);

  // 处理节点展开
  const handleNodeExpand = useCallback(
    async (node: TreeNode) => {
      if (node.type === 'department' && !node.isLoaded) {
        const children = await loadDepartmentData(node.id, node.level);

        setTreeData((prevData) => {
          const updateNode = (nodes: TreeNode[]): TreeNode[] => {
            return nodes.map((n) => {
              if (n.id === node.id) {
                return { ...n, children, isLoaded: true };
              }
              if (n.children) {
                return { ...n, children: updateNode(n.children) };
              }
              return n;
            });
          };
          return updateNode(prevData);
        });
      }
    },
    [loadDepartmentData],
  );

  // 处理添加协作者
  const handleAddCollaborator = useCallback((node: TreeNode) => {
    // TODO: 实现添加协作者逻辑
    alert(`添加协作者: ${node.name}`);
  }, []);

  // 渲染树节点
  const renderTreeNode = useCallback(
    (node: TreeNode): React.ReactNode => {
      if (node.type === 'department') {
        return (
          <Collapse.Panel
            key={node.id}
            title={<DepartmentItem node={node} onAddClick={handleAddCollaborator} />}
            onClick={() => handleNodeExpand(node)}
          >
            {node.children?.map((child) => renderTreeNode(child))}
          </Collapse.Panel>
        );
      } else {
        return (
          <div key={node.id}>
            <UserItem node={node} onAddClick={handleAddCollaborator} />
          </div>
        );
      }
    },
    [handleAddCollaborator, handleNodeExpand],
  );

  return (
    <div className={styles.collaboratorStruct}>
      {/* 组织架构树 */}
      <div className={styles.treeContainer}>
        <Collapse arrowIcon={null} defaultActiveKey={expandedKeys}>
          {treeData.map((node) => renderTreeNode(node))}
        </Collapse>
      </div>
    </div>
  );
}
