import {
  CollaboratorCardButton,
  CollaboratorCardSpan,
  CollaboratorEmpty,
  CollarboratorCard,
  CollarboratorCardAvatar,
  CollarboratorCardConent,
  CollarboratorCardLeft,
  CollarboratorCardName,
  CollarboratorCardRole,
} from './Collaborator';

// 权限枚举定义
export enum PermissionType {
  INHERIT = 'inherit', // 继承权限
  READ_ONLY = 'read_only', // 只能阅读
  COMMENT_ONLY = 'comment_only', // 只能评论
  EDIT = 'edit', // 可以编辑
  NO_ACCESS = 'no_access', // 没有权限
}

// 权限显示文本映射
const PermissionDisplayMap = {
  [PermissionType.INHERIT]: '继承权限',
  [PermissionType.READ_ONLY]: '只能阅读',
  [PermissionType.COMMENT_ONLY]: '只能评论',
  [PermissionType.EDIT]: '可以编辑',
  [PermissionType.NO_ACCESS]: '没有权限',
};

// 获取权限显示文本的函数
function getPermissionDisplayText(permission: PermissionType | string): string {
  return PermissionDisplayMap[permission as PermissionType] || '未知权限';
}

// 判断是否为管理员权限（禁用状态）
function isAdminPermission(data: any): boolean {
  return true;
  // TODO: 根据实际后端字段调整判断逻辑
  // 示例：可能是 data.is_admin 或 data.role === 'admin' 等
  return data.is_admin || data.role === 'admin';
}

// 获取用户当前权限类型
function getUserPermission(data: any): PermissionType {
  // TODO: 根据实际后端接口字段映射权限类型
  // 示例映射逻辑：
  if (data.permission_level === 'admin') return PermissionType.EDIT;
  if (data.permission_level === 'editor') return PermissionType.EDIT;
  if (data.permission_level === 'commenter') return PermissionType.COMMENT_ONLY;
  if (data.permission_level === 'viewer') return PermissionType.READ_ONLY;
  if (data.inherit_permission) return PermissionType.INHERIT;

  // 默认返回只能阅读
  return PermissionType.READ_ONLY;
}

export type CollaboratorItemProps = {
  avatar: string;
  name: string;
  email: string;
  showBtn: boolean;
  displayText: string;
};

export function CollaboratorParentItem({
  showBtn,
  displayText,
  open,
  avatar,
  name,
  email,
}: CollaboratorItemProps & { open?: () => void }) {
  return (
    <CollarboratorCardConent>
      <CollarboratorCardLeft>
        <CollarboratorCardAvatar avatar={avatar} />
        <CollarboratorCardRole email={email} name={name} open={open} />
      </CollarboratorCardLeft>
      {showBtn ? (
        <CollaboratorCardButton text={displayText} onClick={() => {}} />
      ) : (
        <CollaboratorCardSpan text={displayText} />
      )}
    </CollarboratorCardConent>
  );
}

export function CollaboratorRoleItem({
  showBtn,
  displayText,
  onClick,
  avatar,
  name,
  email,
}: CollaboratorItemProps & {
  onClick?: () => void;
}) {
  return (
    <CollarboratorCardConent>
      <CollarboratorCardLeft>
        <CollarboratorCardAvatar avatar={avatar} />
        <CollarboratorCardName email={email} name={name} />
      </CollarboratorCardLeft>
      {showBtn ? (
        <CollaboratorCardButton text={displayText} onClick={onClick} />
      ) : (
        <CollaboratorCardSpan text={displayText} />
      )}
    </CollarboratorCardConent>
  );
}

export function CollaboratorItem({
  data,
  open,
  onClick,
}: {
  data: CollaboratorItemProps;
  open?: () => void;
  onClick?: () => void;
}) {
  const showBtn = isAdminPermission(data);

  const userPermission = getUserPermission(data);
  const displayText = getPermissionDisplayText(userPermission);
  const isParentCollaborator = true;
  if (isParentCollaborator) {
    return (
      <CollaboratorParentItem
        avatar={data.avatar}
        displayText={displayText}
        email={data.email}
        name={data.name}
        open={open}
        showBtn={showBtn}
      />
    );
  }
  return (
    <CollaboratorRoleItem
      avatar={data.avatar}
      displayText={displayText}
      email={data.email}
      name={data.name}
      showBtn={showBtn}
      onClick={onClick}
    />
  );
}

export function CollaboratorList({
  data,
  open,
  onClick,
}: {
  data: (CollaboratorItemProps & { id: number })[];
  open?: () => void;
  onClick?: () => void;
}) {
  return (
    <CollarboratorCard>
      {data.length > 0 ? (
        data.map((item) => <CollaboratorItem key={item.id} data={item} open={open} onClick={onClick} />)
      ) : (
        <CollaboratorEmpty />
      )}
    </CollarboratorCard>
  );
}
