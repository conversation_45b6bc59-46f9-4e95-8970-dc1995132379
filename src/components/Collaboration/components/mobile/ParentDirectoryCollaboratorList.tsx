import type { CollaboratorsData } from '@/api/Collaboration.type';

import {
  CollaboratorCardSpan,
  CollarboratorCard,
  CollarboratorCardAvatar,
  CollarboratorCardConent,
  CollarboratorCardLeft,
  CollarboratorCardName,
} from './Collaborator';

export function ParentDirectoryCollaboratorList({ data }: { data: CollaboratorsData['roles'] }) {
  return (
    <CollarboratorCard>
      {data.map((item) => (
        <CollarboratorCardConent key={item.id}>
          <CollarboratorCardLeft>
            <CollarboratorCardAvatar avatar={item.avatar} />
            <CollarboratorCardName email={item.email} name={item.name} />
          </CollarboratorCardLeft>
          <CollaboratorCardSpan text="可以编辑" />
        </CollarboratorCardConent>
      ))}
    </CollarboratorCard>
  );
}
