import { CaretDownOutlined, CheckOutlined } from '@ant-design/icons';
import { Button, Dropdown, Space } from 'antd';
import React from 'react';

import { ReactComponent as DarkPlusSign } from '@/assets/images/svg/darkPlusSign.svg';
import { fm2 } from '@/modules/Locale';

import { itemRole } from '.';

interface DropdownMenuItemRoleProps {
  data: {
    isAdmin?: boolean;
    role?: string;
    user?: {
      name: string;
    };
  };
  dropdownChangeRole: (info: { key: string }, props: unknown, type: string) => void;
  type?: string;
  canManageCollaborator: boolean;
}

interface ItemRole {
  key: string;
  danger?: boolean;
  label: string;
}

const DropdownMenuItemRole: React.FC<DropdownMenuItemRoleProps> = ({
  data,
  dropdownChangeRole,
  type,
  canManageCollaborator,
}) => {
  const getItemName = (): string => {
    const matchedItem = itemRole.find((item: { key: string; label: string }) => item.key === data?.role);
    if (matchedItem) {
      return matchedItem.label;
    }
    if (data?.isAdmin) {
      return fm2('ShareCollaboration.admin');
    }
    return fm2('ShareCollaboration.addPermission');
  };

  return (
    <Dropdown
      menu={{
        items: itemRole.map((roleItem: ItemRole) => ({
          key: roleItem.key,
          danger: roleItem.danger,
          label: data?.role ? (
            <div className="dropdownItem">
              <div>{roleItem.label}</div>
              {data?.role === roleItem.key && <CheckOutlined />}
            </div>
          ) : roleItem.key === 'remove' ? (
            <div className="removeDivNone" />
          ) : (
            <div className="dropdownItem">
              <div>{roleItem.label}</div>
            </div>
          ),
        })),
        onClick: (info) => dropdownChangeRole(info, data, type ?? ''),
      }}
      placement="bottomRight"
      trigger={['click']}
    >
      <Space>
        <Button
          disabled={data?.isAdmin || !canManageCollaborator}
          icon={data?.isAdmin || data?.role ? <CaretDownOutlined /> : <DarkPlusSign />}
          iconPosition="end"
          size="small"
          type="text"
        >
          {getItemName()}
        </Button>
      </Space>
    </Dropdown>
  );
};

export default DropdownMenuItemRole;
