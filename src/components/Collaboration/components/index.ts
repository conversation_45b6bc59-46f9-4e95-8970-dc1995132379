import { message } from 'antd';

import { fm2 } from '@/modules/Locale';

export const copyLinkUrl = (url: string) => {
  navigator.clipboard
    .writeText(url)
    .then(() => {
      message.success(fm2('ShareCollaboration.copySuccess'));
    })
    .catch(() => {
      message.error(fm2('ShareCollaboration.copyFail'));
    });
};

export const optionsDays = [
  { value: 1, label: `1${fm2('ShareCollaboration.day')}` },
  { value: 7, label: `7${fm2('ShareCollaboration.day')}` },
  { value: 30, label: `30${fm2('ShareCollaboration.day')}` },
];

export const startCountdown = (shareModeExpiredAt: number, setRemainingTime: (time: string) => void): number => {
  const intervalId = window.setInterval(() => {
    const now = Math.floor(Date.now() / 1000);
    const remainingSeconds = shareModeExpiredAt - now;

    if (remainingSeconds <= 0) {
      clearInterval(intervalId);
      setRemainingTime('00:00:00');
      return;
    }

    const days = Math.floor(remainingSeconds / 86400);
    const hours = Math.floor((remainingSeconds % 86400) / 3600);
    const minutes = Math.floor((remainingSeconds % 3600) / 60);
    const seconds = remainingSeconds % 60;

    let formattedTime: string;

    if (days === 0) {
      formattedTime = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    } else {
      formattedTime = `${String(days).padStart(2, '0')}${fm2('ShareCollaboration.day')}${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    }

    setRemainingTime(formattedTime);
  }, 1000);

  return intervalId;
};

export const stopCountdown = (intervalId: number | null): void => {
  if (intervalId) {
    clearInterval(intervalId);
  }
};

export const itemsPrenet = [
  { key: 'inherited', label: fm2('ShareCollaboration.inheritPermission') },
  { key: 'reader', label: fm2('ShareCollaboration.readOnly') },
  { key: 'commentator', label: fm2('ShareCollaboration.comment') },
  { key: 'editor', label: fm2('ShareCollaboration.commentAndEdit') },
  { key: 'none', label: fm2('ShareCollaboration.forbidAccess') },
];
export const itemRole = [
  { key: 'reader', label: fm2('ShareCollaboration.readOnly') },
  { key: 'commentator', label: fm2('ShareCollaboration.comment') },
  { key: 'editor', label: fm2('ShareCollaboration.commentAndEdit') },
  { key: 'none', label: fm2('ShareCollaboration.forbidAccess') },
  { key: 'remove', label: fm2('ShareCollaboration.removePermission'), danger: true },
];
export const itemsAdmin: any = [
  { key: 'merger', label: fm2('ShareCollaboration.admin') },
  { key: 'remove', label: fm2('ShareCollaboration.removeManager'), danger: true },
];

import { getCollaborationList } from '@/api/Collaboration';
export const getMergerAccount = async (Arr: any[], guid: string, callback?: (updatedArr: any[]) => void) => {
  const getUserData = await getCollaborationList(guid, { includeInherited: false, includeAdmin: true });
  Arr?.forEach((el: any) => {
    el.isAdmin = false;
    getUserData?.data?.admins?.forEach((e: { id: any; isInherited: any }) => {
      if (el.id === e.id || el.user?.id === e.id || el.department?.id === e.id) {
        el.isAdmin = true;
        el.isInherited = e.isInherited;
      }
    });
  });
  if (callback) {
    callback([...Arr]);
  }
};

export const getMergerRoles = async (Arr: any[], guid: string, callback?: (updatedArr: any[]) => void) => {
  const getUserData = await getCollaborationList(guid, { includeInherited: false, includeAdmin: true });
  Arr.forEach((el: any) => {
    getUserData?.data?.admins?.forEach((e: { id: any; isInherited: any }) => {
      if (el.id === e.id || el.user?.id === e.id || el.department?.id === e.id) {
        el.isAdmin = true;
        el.isInherited = e.isInherited;
      }
    });
    el.role = null;
    getUserData?.data?.roles?.forEach((e: { id: any; role: any }) => {
      if (el.id === e.id || el.user?.id === e.id || el.department?.id === e.id) {
        el.role = e.role;
      }
    });
  });
  if (callback) {
    callback([...Arr]);
  }
};
