//协作与分享页面样式

.collaborationModal {
  font-size: 12px;

  .ant-modal-content {
    padding: 0;

    .ant-modal-close {
      top: 12px;
      inset-inline-end: 20px;
    }

    .ant-modal-header {
      margin: 0 24px;
      padding-top: 16px;
      height: 40px;
      display: flex;
      align-items: center;

      .ant-modal-title {
        font-size: 16px;

        .modlaArrow {
          display: flex;
          align-items: center;

          span {
            padding-left: 2px;
          }
        }
      }
    }

    .ant-modal-body {
      margin: 10px 24px 16px;
    }

    .ant-modal-footer {
      .shareFooterWrapper {
        width: 100%;
      }

      .shareFooter {
        width: 100%;
        height: 56px;
        border-radius: 0 0 4px 4px;
        background: var(--theme-chart-color-bg);
        display: flex;
        justify-content: flex-end;
        padding: 8px 24px 16px;
        box-sizing: border-box;
      }

      .qrCodeBox {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .qrLeft {
          display: flex;
          align-items: center;
          font-size: 13px;

          .qrTitle {
            margin-right: 8px;
          }

          .qrCode {
            margin-right: 8px;
          }

          .ant-btn.ant-btn-icon-only {
            width: 32px;
            height: 32px;
            padding: 4px;
          }
        }

        .qrRight {
          display: flex;
          align-items: center;

          span {
            font-size: 12px;
          }
        }
      }
    }
  }
}
//单独分享
.shareLinkCopy {
  display: flex;

  .inputDisabled {
    margin-right: 8px;
    font-size: 12px;
  }

  button {
    font-size: 12px;
    font-weight: 500;
  }
}

.modalBodyInput {
  .ant-input-affix-wrapper > input.ant-input {
    font-size: 12px;
  }
}

.searchBox {
  height: 300px;
  overflow-y: auto;
}

.userList {
  font-size: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 42px;
  border: 1px solid var(--theme-box-shadow-color-level10);
  border-radius: 2px;
  margin: 10px 0 16px;
  padding: 0 0 0 8px;
  background-color: var(--theme-layout-color-bg-editor);
  cursor: pointer;

  .collaborator {
    margin-right: 4px;
  }

  .noDataCollaborator {
    display: flex;
    align-items: center;
    padding: 2px 4px 2px 2px;
    background-color: var(--theme-table-color-header-gray-bg);
    border-radius: 24px;

    span {
      margin-left: 4px;
      color: var(--theme-text-color-disabled);
    }
  }

  .manager {
    margin-left: 16px;
    margin-right: 4px;
  }

  .userItem {
    display: flex;
    align-items: center;
  }

  .userRightIcon {
    padding: 12px 10px 8px 16px;
    cursor: pointer;
  }
}

.linkShareTitle {
  font-weight: 500;
  height: 24px;
  line-height: 24px;
  font-size: 12px;
  margin-bottom: 10px;
}

.switchBox {
  font-size: 12px;
  display: flex;
  justify-content: left;
  align-items: center;
  height: 20px;
  color: var(--theme-text-color-secondary);
}

.switchOpenShare {
  padding-top: 10px;

  .linkCopy {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .linkText {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex: 1;
      white-space: nowrap;
      margin-right: 10px;

      .ant-select-selector {
        font-size: 12px;
      }
    }

    button {
      font-size: 12px;
      font-weight: 500;
    }
  }

  .linkSetting {
    font-size: 12px;
    color: var(--theme-text-color-secondary);
    border: 1px solid var(--theme-box-shadow-color-level10);
    background-color: var(--theme-layout-color-bg-editor);
    margin: 10px 0 16px;
    border-radius: 2px;
    padding: 10px 8px;

    .linkPassword {
      display: flex;
      align-items: center;
      height: 24px;
      margin-bottom: 10px;

      .linkPasswordInput {
        border: 1px solid var(--theme-box-shadow-color-level10);
        border-radius: 2px;
        padding: 2px 4px;
        margin: 0 8px;
        width: 62px;
        text-align: center;
        color: var(--theme-text-color-medium);
      }

      .changePassword {
        margin-left: 4px;
        cursor: pointer;
      }
    }

    .linkValid {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 24px;

      .linkValidLeft {
        display: flex;
        align-items: center;
      }

      .validSelect {
        width: 80px;
        margin: 0 8px;

        .ant-select-selector {
          font-size: 12px;
        }
      }
    }
  }
}

.switchMr {
  margin-right: 8px;
}

.tooltipShare {
  text-align: center;
  width: 157px;
  padding: 5px 11px;
}

// 协作者列表样式

.collaborationList {
  .listTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 24px;
    font-weight: 500;
    margin: 10px 0;

    .listTitleRight {
      display: flex;
      justify-content: space-between;
      color: var(--theme-basic-color-guidance);
      cursor: pointer;
      font-size: 12px;

      span {
        margin-right: 4px;
      }
    }
  }
}

.listBox {
  max-height: 150px;
  overflow-y: auto;
  border: 1px solid var(--theme-box-shadow-color-level10);
  background-color: var(--theme-layout-color-bg-editor);
  border-radius: 2px;
  padding: 8px 8px 8px 0;
  //修改antd样式
  .ant-collapse-item {
    .ant-collapse-header {
      padding: 0;
    }

    .ant-collapse-content {
      padding: 0;
    }
  }

  .listEmpty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 116px;
  }
}

.marginBottom16 {
  margin-bottom: 16px;
  max-height: 238px;
}

.listItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0 8px 8px;
}

.spaceCollaboration {
  .ant-btn {
    font-size: 12px;
  }

  .noClick {
    font-size: 12px;
    color: var(--theme-text-color-disabled);
  }
}

.ant-collapse-borderless > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box {
  padding: 0 30px 0 16px;
  background-color: inherit;
}

.itemLeft {
  display: flex;
  align-items: center;
  font-size: 12px;
  flex: 1;

  .ant-avatar {
    border: none;
  }

  .superOrg {
    margin-left: 8px;
  }

  .itemRight {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;

    .itemName {
      width: 72px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin: 0 10px;
    }

    .emailText {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 148px;
    }

    .itemNameDep {
      margin-left: 8px;
    }
  }
}

.collapseBtn {
  display: inline-block;
  margin-left: 4px;
  color: var(--theme-basic-color-guidance);

  .collapseClick {
    display: flex;
    align-items: center;
  }

  span {
    padding: 0 4px;
  }
}

.dropdownItem {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  width: 118px;
}

//添加协作者组织人员
.collaborationAdd {
  .listItem:hover {
    background-color: var(--theme-text-button-color-hover);
  }

  .ant-btn {
    font-size: 12px;
  }

  .itemTabs {
    .ant-tabs-content-holder {
      min-height: 300px;
    }
  }

  .tabboxMaxH {
    max-height: 300px;
    overflow: auto;
  }

  .ant-tabs-nav {
    margin-bottom: 8px;
  }

  .ant-tabs-nav-list {
    .ant-tabs-tab,
    .ant-tabs-tab-active {
      font-size: 13px;
      padding: 12px 0 10px;
    }
  }

  .ant-collapse-item,
  .ant-collapse-item-active {
    .ant-collapse-header {
      padding: 0;
    }

    .ant-collapse-content-box {
      padding: 0 0 0 8px;
    }

    .ant-collapse-header .ant-collapse-expand-icon {
      padding: 20px 4px 22px 0;
      padding-inline-end: 4px;
    }
  }

  .ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box {
    padding-block: 0;
  }

  .ant-collapse-arrow {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
  }

  .ant-collapse-item-active .ant-collapse-arrow {
    transform: rotate(90deg);
  }

  .addBottom {
    padding-top: 12px;
    border-top: 1px solid var(--theme-separator-color-lighter);
    font-size: 12px;
    color: var(--theme-text-color-secondary);
  }
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
  margin-left: 8px;
}

.selectLabel {
  color: var(--theme-basic-color-guidance);
}

.noDataInherited {
  padding: 4px 2px 8px 4px;
  font-size: 12px;
  color: var(--theme-icon-info-color);
}

.noDataDepartment {
  padding: 4px 10px;
  font-size: 12px;
  color: var(--theme-icon-info-color);
  border: 1px solid var(--theme-separator-color-lighter);
  border-radius: 2px;
  background: var(--theme-layout-color-bg-editor);
}

.CollapseDepartment {
  max-height: 256px;
  overflow: auto;
}

.ant-select-dropdown .ant-select-item-option-content {
  font-size: 12px;

  .dropdownItem {
    font-size: 12px;
  }
}

.ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: var(--theme-text-button-color-active);
}

.ant-dropdown .ant-dropdown-menu {
  .ant-dropdown-menu-item:has(.removeDivNone) {
    padding: 0;
  }
}

.ant-popconfirm {
  .ant-popover-inner-content {
    width: 232px;
    padding: 12px;
  }

  .ant-popconfirm-description {
    font-size: 13px;
  }

  .ant-popconfirm-buttons {
    display: flex;
    flex-direction: row-reverse;

    .ant-btn-variant-solid {
      background: var(--theme-button-color-alert-active);
      border: 1px solid var(--theme-button-color-alert-active);
    }

    .ant-btn-variant-solid:not(:disabled):not(.ant-btn-disabled):hover {
      background: var(--theme-button-color-alert-active);
      border: 1px solid var(--theme-button-color-alert-active);
    }
  }
}
