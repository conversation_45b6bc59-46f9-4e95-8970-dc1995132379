export interface CollaborationShareProps {
  visible: boolean;
  guid: string;
  onCancel: () => void;
  enterType?: string;
}

export interface CollaborationData {
  password: string;
  passwordProtected: boolean;
  type: string;
  isSpace: boolean | undefined;
  role?: string;
  url?: string;
  guid?: string;
  parentId?: number;
  parentRole?: string;
  isAdmin?: boolean;
  isFileAdmin?: boolean;
  isFolder?: boolean;
  shareMode?: string;
  name?: string;
  permissionsAndReasons?: {
    canChangeShareMode: { value: boolean; reason: string };
    canExit?: { value: boolean; reason: string };
    canManageCollaborator?: { value: boolean; reason: string };
    canManageAdmin?: { value: boolean; reason: string };
  };
  isShortcut?: boolean;
  shortcutSource?: {
    url: string;
    guid: string;
  };
  shareModeExpireDuration?: number;
}
export interface Role {
  id: number;
  avatar?: string;
  name?: string;
}
export interface UseCollaborationDetailResult {
  data: CollaborationData | null;
  constUrl: string;
  fullUrl: string;
  setFullUrl: (url: string) => void;
}
