import { useEffect, useState } from 'react';

import type { CollaborationData } from '../types';
// 分享的状态
export const useShareStatus = (data: CollaborationData | null | undefined) => {
  const [shareStatus, setShareStatus] = useState<string>('');
  useEffect(() => {
    if (data?.shareMode) {
      setShareStatus(data.shareMode);
    } else {
      setShareStatus('');
    }
  }, [data]);
  return shareStatus;
};
