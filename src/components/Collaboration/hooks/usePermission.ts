import { useMemo } from 'react';

import type { CollaborationData } from '../types';
//判断是否有协作权限
export function useRoleCheck(data: CollaborationData | null | undefined): boolean {
  return useMemo(() => {
    return !!(data?.role && data?.role !== 'none');
  }, [data]);
}
// 判断文件是否可以公开分享权限（是否可以操作）；
export const useShareDisabled = (data: CollaborationData | null | undefined): boolean => {
  return useMemo(() => {
    if (!data) return true;
    const canChangeShareMode = data?.permissionsAndReasons?.canChangeShareMode?.value ?? false;
    const isSpecialType = ['folder', 'newdoc', 'modoc', 'mosheet', 'table', 'presentation', 'form'].includes(
      data.type || '',
    );
    if (data.isSpace || data.isFolder || !isSpecialType) {
      return true;
    }
    return !canChangeShareMode;
  }, [data]);
};
// 判断协作者权限
export const useCanManageCollaborator = (data: CollaborationData | null | undefined): boolean => {
  const canManageCollaborator = useMemo(() => {
    return data?.permissionsAndReasons?.canManageCollaborator?.value ?? false;
  }, [data]);

  return canManageCollaborator;
};
// 本人能不能退出协作者权限
export const useCanExit = (data: CollaborationData | null | undefined): boolean => {
  const canExit = useMemo(() => {
    return data?.permissionsAndReasons?.canExit?.value ?? false;
  }, [data]);

  return canExit;
};

//判断管理者权限
export const useCanManageAdmin = (data: CollaborationData | null | undefined): boolean => {
  const canManageAdmin = useMemo(() => {
    return data?.permissionsAndReasons?.canManageAdmin?.value ?? false;
  }, [data]);

  return canManageAdmin;
};
//分享开关是开还是关
export const useShareStatus = (shareStatus: string): boolean => {
  return useMemo(() => {
    return shareStatus !== 'private' && shareStatus !== '';
  }, [shareStatus]);
};
//密码开关是开还是关
export const useShareModePassword = (data: CollaborationData) => {
  return useMemo(() => {
    const shareMode = data?.shareMode;
    const passwordProtected = data?.passwordProtected;
    const isValid = shareMode !== 'private' && passwordProtected;
    const password = data?.password;
    return {
      isValid,
      password,
    };
  }, [data]);
};
