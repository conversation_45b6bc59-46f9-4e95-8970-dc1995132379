import { UserOutlined } from '@ant-design/icons';
import type { TabsProps } from 'antd';
import { Avatar, Collapse, Input, message, Switch, Tabs, Tooltip } from 'antd';
import { debounce } from 'lodash';
import { useCallback, useEffect, useRef, useState } from 'react';

import {
  addCollaborationDepartment,
  deleteAdmin,
  deleteCollaboration,
  deleteCollaborationDepartment,
  deleteDepAdmin,
  getOrgDepartment,
  getOrgDepartmentUser,
  getRecentContact,
  getSearchUser,
  setAdmin,
  setDepAdmin,
  updateCollaboration,
} from '@/api/Collaboration';
import { ReactComponent as ArrowTop } from '@/assets/images/svg/arrowTop.svg';
import { ReactComponent as DarkArrowRight } from '@/assets/images/svg/darkArrowRight.svg';
import { ReactComponent as Organization } from '@/assets/images/svg/organization.svg';
import { ReactComponent as Search } from '@/assets/images/svg/search.svg';
import { ReactComponent as TopLevel } from '@/assets/images/svg/topLevel.svg';
import { fm2 } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

import deleteConfirm from '../fileList/components/deleteConfirm';
import { getMergerAccount, getMergerRoles } from './components';
import DropdownMenuItemAdmin from './components/DropdownMenuItemAdmin';
import DropdownMenuItemRole from './components/DropdownMenuItemRole';

interface CollaborationAddProps {
  addAdminsOrRoles: string;
  guid: string;
  canExit: boolean;
  canManageCollaborator: boolean;
}
export const CollaborationAdd: React.FC<CollaborationAddProps> = ({
  addAdminsOrRoles,
  guid,
  canExit,
  canManageCollaborator,
}) => {
  const [recentData, setRecentData] = useState<any>([]);
  const [currentDepartment, setCurrentDepartment] = useState<any>([]);
  const [subdepartments, setSubdepartments] = useState<any>([]);
  const [departmentDataMap, setDepartmentDataMap] = useState<Record<string, any[]>>({});
  const [inputValue, setInputValue] = useState<string>('');
  const [switchCheckedValid, setSwitchCheckedValid] = useState<boolean>(true);
  const [clickDepId, setClickDepId] = useState<number | string>(1);
  const meId = useMeStore((state) => state.me.id);
  interface SearchResultUser {
    user: {
      id: string;
      name: string;
      avatar?: string;
      email?: string;
    };
    department?: never;
  }
  interface SearchResultDepartment {
    department: {
      id: string;
      name: string;
    };
    user?: never;
  }
  type SearchResultItem = SearchResultUser | SearchResultDepartment;
  const [searchResult, setSearchResult] = useState<SearchResultItem[]>([]);

  const getRecent = useCallback(() => {
    getRecentContact().then((res: any) => {
      if (addAdminsOrRoles === 'admins') {
        getMergerAccount(res.data.results, guid, setRecentData);
      } else {
        getMergerRoles(res.data.results, guid, setRecentData);
      }
    });
  }, [addAdminsOrRoles, guid]);
  const getDepartment = (id: string) => {
    if (departmentDataMap[id]) return;
    getOrgDepartment(id).then((res: any) => {
      const currentDept = res.data.currentDepartment;
      const subDepts = res.data.subdepartments;
      getOrgDepartmentUser(currentDept.id, { perPage: 500, page: 1 }).then((userRes: any) => {
        const users = userRes.data.users || [];
        const updatedList = [...subDepts, ...users];
        if (id === '1') {
          if (addAdminsOrRoles === 'admins') {
            getMergerAccount([currentDept], guid, setCurrentDepartment);
            getMergerAccount(updatedList, guid, setSubdepartments);
          } else {
            getMergerRoles([currentDept], guid, setCurrentDepartment);
            getMergerRoles(updatedList, guid, setSubdepartments);
          }
        } else {
          setClickDepId(id);
          if (addAdminsOrRoles === 'admins') {
            getMergerAccount(updatedList, guid, (data) => {
              setDepartmentDataMap((prev) => ({
                ...prev,
                [id]: data,
              }));
            });
          } else {
            getMergerRoles(updatedList, guid, (data) => {
              setDepartmentDataMap((prev) => ({
                ...prev,
                [id]: data,
              }));
            });
          }
        }
      });
    });
  };
  const onChangeTab = (key: string) => {
    if (key === 'recently') {
      getRecent();
    }
    if (key === 'department') {
      getDepartment('1');
    }
  };
  const handleCollapseTwo = (id: string) => {
    getDepartment(id);
  };
  const updatePermissions = (item: any) => {
    if (item.department || item.user) {
      getMergerAccount(searchResult, guid, setSearchResult);
    } else {
      getMergerAccount(recentData, guid, setRecentData);
      if (item.id === 1) {
        getMergerAccount(currentDepartment, guid, setCurrentDepartment);
      } else {
        getMergerAccount(subdepartments, guid, setSubdepartments);
        const data = departmentDataMap[clickDepId];
        if (data) {
          getMergerAccount(data, guid, (updatedData) => {
            setDepartmentDataMap((prev) => ({
              ...prev,
              [clickDepId]: updatedData,
            }));
          });
        }
      }
    }
  };
  const dropdownChangeAdmin = async (itemData: any, info?: { key: string }, type?: string) => {
    if (info?.key === 'remove') {
      const isDepartment = type === 'department' || itemData.department;
      const id = itemData.id || (isDepartment ? itemData.department?.id : itemData.user?.id);
      const deleteConfig = {
        i18nText: {
          title: fm2('ShareCollaboration.confirmRemoveCollaborator'),
          content: fm2('ShareCollaboration.removeCollaborator', { name: itemData.name }),
          okText: fm2('ShareCollaboration.confirmRemove'),
          cancelText: fm2('ShareCollaboration.cancel'),
          success: fm2('ShareCollaboration.success'),
          error: fm2('ShareCollaboration.failed'),
        },
        data: { guid, id, name: itemData.name },
        callback: () => updatePermissions(itemData),
      };
      if (isDepartment) {
        deleteConfirm({
          ...deleteConfig,
          api: () => deleteDepAdmin(guid, id),
        });
      } else {
        deleteConfirm({
          ...deleteConfig,
          api: () => deleteAdmin(guid, id),
        });
      }
    }
    if (!itemData.isAdmin) {
      const isDepartment = type === 'department' || itemData.department;
      const id = itemData.id || (isDepartment ? itemData.department?.id : itemData.user?.id);
      const res = isDepartment
        ? await setDepAdmin(guid, id, { needNotice: switchCheckedValid })
        : await setAdmin(guid, id, { needNotice: switchCheckedValid });
      if (res?.status === 204) {
        updatePermissions(itemData);
        message.success(fm2('ShareCollaboration.addSuccess'));
      }
    }
  };
  //协作者的操作
  const getTargetId = (data: any): string => {
    return data.id || data?.department?.id || data?.user?.id;
  };
  const updatePermissionsRoles = (item: any) => {
    if (item.department || item.user) {
      getMergerRoles(searchResult, guid, setSearchResult);
    } else {
      getMergerRoles(recentData, guid, setRecentData);
      if (item.id === 1) {
        getMergerRoles(currentDepartment, guid, setCurrentDepartment);
      } else {
        getMergerRoles(subdepartments, guid, setSubdepartments);

        const data = departmentDataMap[clickDepId];
        if (data) {
          getMergerRoles(data, guid, (updatedData) => {
            setDepartmentDataMap((prev) => ({
              ...prev,
              [clickDepId]: updatedData,
            }));
          });
        }
      }
    }
  };
  const dropdownChangeRole = async (info: { key: string }, data: any, type: string) => {
    const isRemove = info.key === 'remove';
    let isDepartment = !data?.avatar || data?.departmentId || type === 'department';
    if (data?.type === 'department') {
      isDepartment = true;
    } else if (data?.type === 'user') {
      isDepartment = false;
    }
    let res;
    if (isRemove) {
      if (meId === data.id && !canExit) {
        // 判断本人是否可以退出协作
        message.warning(fm2('ShareCollaboration.noPermissionCollaboration'));
        return false;
      }
      res = isDepartment
        ? await deleteCollaborationDepartment(guid, getTargetId(data))
        : await deleteCollaboration(guid, getTargetId(data));
      if (res.status === 204) {
        updatePermissionsRoles(data);
        message.success(
          isDepartment
            ? fm2('ShareCollaboration.deleteDepartmentSuccess')
            : fm2('ShareCollaboration.deleteUserSuccess'),
        );
      }
    } else {
      res = isDepartment
        ? await addCollaborationDepartment(guid, getTargetId(data), { role: info.key, needNotice: switchCheckedValid })
        : await updateCollaboration(guid, getTargetId(data), { role: info.key, needNotice: switchCheckedValid });

      if (res.status === 204 || res.status === 200) {
        updatePermissionsRoles(data);
        message.success(fm2('ShareCollaboration.operationSuccess'));
      }
    }
  };
  const debouncedSearch = useRef(
    debounce((value) => {
      if (value.trim() !== '') {
        getSearchUser({
          limit: 100,
          keyword: value,
          filter: {
            user: {
              includeRecentContact: true,
              includeTeamMember: true,
            },
            department: {},
            group: {},
          },
          fetchFileRoleByFileGuid: guid,
        }).then((res) => {
          if (addAdminsOrRoles === 'admins') {
            getMergerAccount(res.data?.results, guid, setSearchResult);
          } else {
            getMergerRoles(res.data?.results, guid, setSearchResult);
          }
        });
      }
    }, 300),
  ).current;
  //组织结构第归树部门
  const renderCollapseItems = (subdepartments: any) => {
    return (
      <div className="CollapseDepartment">
        {subdepartments.length > 0 ? (
          subdepartments.map((item: any) =>
            item?.handoverMenu ? (
              <div key={item.id} className="listItem">
                <div className="itemLeft">
                  <Avatar icon={<UserOutlined />} size={28} src={item.avatar} />
                  <div className="itemRight">
                    <div className="itemName">{item.name}</div>
                    <Tooltip placement="top" title={item.email}>
                      <div className="emailText">{item.email}</div>
                    </Tooltip>
                  </div>
                </div>
                {addAdminsOrRoles === 'admins' ? (
                  <DropdownMenuItemAdmin dropdownChangeAdmin={dropdownChangeAdmin} itemData={item} />
                ) : (
                  <DropdownMenuItemRole
                    canManageCollaborator={canManageCollaborator}
                    data={item}
                    dropdownChangeRole={dropdownChangeRole}
                  />
                )}
              </div>
            ) : (
              <div key={item.id}>
                <Collapse
                  accordion
                  collapsible="icon"
                  expandIcon={({ isActive }) => <span>{isActive ? <DarkArrowRight /> : <ArrowTop />}</span>}
                  ghost={true}
                  items={[
                    {
                      key: item.id,
                      label: (
                        <div className="listItem">
                          <div className="itemLeft">
                            <Avatar icon={<Organization />} size={28} />
                            <div className="itemRight">
                              <div className="itemNameDep">{item.name}</div>
                            </div>
                          </div>
                          {addAdminsOrRoles === 'admins' ? (
                            <DropdownMenuItemAdmin
                              dropdownChangeAdmin={dropdownChangeAdmin}
                              itemData={item}
                              type={'department'}
                            />
                          ) : (
                            <DropdownMenuItemRole
                              canManageCollaborator={canManageCollaborator}
                              data={item}
                              dropdownChangeRole={dropdownChangeRole}
                              type={'department'}
                            />
                          )}
                        </div>
                      ),
                      children: departmentDataMap[item.id] ? renderCollapseItems(departmentDataMap[item.id]) : null,
                    },
                  ]}
                  onChange={(keys) => {
                    keys.forEach((key) => handleCollapseTwo(key));
                  }}
                />
              </div>
            ),
          )
        ) : (
          <div className="noDataDepartment">{fm2('ShareCollaboration.noChildDepartment')}</div>
        )}
      </div>
    );
  };
  const tabItems: TabsProps['items'] = [
    {
      key: 'recently',
      label: fm2('ShareCollaboration.recent'),
      children: (
        <div className="tabboxMaxH">
          {recentData.map((item: any) => {
            return (
              <div key={item.id} className="listItem">
                <div className="itemLeft">
                  <Avatar icon={<UserOutlined />} size={28} src={item.avatar} />
                  <div className="itemRight">
                    <div className="itemName">{item.name}</div>
                    <Tooltip placement="top" title={item.email}>
                      <div className="emailText">{item.email}</div>
                    </Tooltip>
                  </div>
                </div>
                {addAdminsOrRoles === 'admins' ? (
                  <DropdownMenuItemAdmin dropdownChangeAdmin={dropdownChangeAdmin} itemData={item} />
                ) : (
                  <DropdownMenuItemRole
                    canManageCollaborator={canManageCollaborator}
                    data={item}
                    dropdownChangeRole={dropdownChangeRole}
                  />
                )}
              </div>
            );
          })}
        </div>
      ),
    },
    {
      key: 'department',
      label: fm2('ShareCollaboration.organization'),
      children: (
        <Collapse
          collapsible="icon"
          defaultActiveKey={['1']}
          expandIcon={({ isActive }) => (
            <span className="ant-collapse-arrow">
              <DarkArrowRight rotate={isActive ? 0 : 90} />
            </span>
          )}
          ghost={true}
          items={[
            {
              key: '1',
              label: (
                <div className="listItem">
                  <div className="itemLeft">
                    <Avatar icon={<TopLevel />} size={28} />
                    <div className="itemRight">
                      <div className="itemNameDep">{currentDepartment[0]?.name}</div>
                    </div>
                  </div>
                  {addAdminsOrRoles === 'admins' ? (
                    <DropdownMenuItemAdmin
                      dropdownChangeAdmin={dropdownChangeAdmin}
                      itemData={currentDepartment[0]}
                      type={'department'}
                    />
                  ) : (
                    <DropdownMenuItemRole
                      canManageCollaborator={canManageCollaborator}
                      data={currentDepartment[0]}
                      dropdownChangeRole={dropdownChangeRole}
                      type={'department'}
                    />
                  )}
                </div>
              ),
              children: renderCollapseItems(subdepartments),
            },
          ]}
        />
      ),
    },
  ];
  useEffect(() => {
    debouncedSearch(inputValue);
    return () => debouncedSearch.cancel();
  }, [inputValue, debouncedSearch]);
  useEffect(() => {
    getRecent();
  }, [getRecent]);
  return (
    <div className="collaborationAdd">
      <div className="modalBodyInput">
        <Input
          placeholder={`${fm2('ShareCollaboration.clickHereToSearchAndAdd')}${
            addAdminsOrRoles === 'admins' ? fm2('ShareCollaboration.admin') : fm2('ShareCollaboration.coauthor')
          }`}
          prefix={<Search />}
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
        />
      </div>
      {inputValue ? (
        <>
          <Tabs
            defaultActiveKey="1"
            items={[{ key: '1', label: fm2('ShareCollaboration.searchResult'), children: null }]}
            size="small"
          />
          <div className="searchBox">
            {searchResult.map((item) => (
              <div key={item.user?.id || item.department?.id}>
                <div className="listItem">
                  <div className="itemLeft">
                    <Avatar
                      icon={item.department ? <Organization /> : <UserOutlined />}
                      size={28}
                      src={item.user?.avatar}
                    />
                    <div className="itemRight">
                      <div className="itemName">{item.user?.name || item.department?.name}</div>
                      {item.user?.email && (
                        <Tooltip placement="top" title={item.user?.email}>
                          <div>{item.user?.email}</div>
                        </Tooltip>
                      )}
                    </div>
                  </div>
                  {addAdminsOrRoles === 'admins' ? (
                    <DropdownMenuItemAdmin dropdownChangeAdmin={dropdownChangeAdmin} itemData={item} />
                  ) : (
                    <DropdownMenuItemRole
                      canManageCollaborator={canManageCollaborator}
                      data={item}
                      dropdownChangeRole={dropdownChangeRole}
                    />
                  )}
                </div>
              </div>
            ))}
          </div>
        </>
      ) : (
        <Tabs className="itemTabs" defaultActiveKey="recently" items={tabItems} size="small" onChange={onChangeTab} />
      )}
      <div className="addBottom">
        <Switch
          checked={switchCheckedValid}
          className="switchMr"
          size="small"
          onChange={(checked) => {
            setSwitchCheckedValid(checked);
          }}
        />
        <span>{fm2('ShareCollaboration.sendNotificationToTheOther')}</span>
      </div>
    </div>
  );
};
