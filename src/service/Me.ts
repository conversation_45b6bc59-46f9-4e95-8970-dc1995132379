import { useCallback } from 'react';
import { useIntl } from 'umi';

import * as meApi from '@/api/Me';
import { useMeStore } from '@/store/Me';

export function useGetMe() {
  const setMe = useMeStore((state) => state.setMe);

  const me = useMeStore((state) => state.me);

  const intl = useIntl();

  return useCallback(async () => {
    return await meApi.getMe().then(
      (response) => {
        setMe(response.data);
      },
      async (error) => {
        if (error.status === 404) {
          const anonymousId = await meApi.getAnonymousId();
          setMe({
            ...me,
            id: anonymousId,
            name: intl.formatMessage({
              id: 'service.Me.anonymousName',
            }),
          });
        }
      },
    );
  }, [setMe]);
}

export function useLogin() {
  return useCallback(async (username: string, password: string) => {
    await meApi.login(username, password);
  }, []);
}
