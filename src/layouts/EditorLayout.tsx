import type { AxiosResponse } from 'axios';
import type { PropsWithChildren } from 'react';
import { useEffect, useState } from 'react';
import { useParams } from 'umi';

import { fileDetail } from '@/api/File';
import { to } from '@/api/Request';
import { EditorHeader } from '@/components/EditorHeader';
import ErrorPageRenderer from '@/components/FileErrorPage';
import PasswordInput from '@/components/PasswordInput/PasswordInput';
import { useMeStore } from '@/store/Me';
import type { ErrorResponse, FileDetail } from '@/types/api';
import { isFillForm, isFormResponseSharePath } from '@/utils/url';

import styles from './EditorLayout.less';

type DetailState = { ok: true; data?: FileDetail } | { ok: false; data?: ErrorResponse };

export default function EditorLayout(props: PropsWithChildren<{ style?: React.CSSProperties }>) {
  const params = useParams<{ guid: string }>();
  const guid = params?.guid || '';
  const [detail, setDetail] = useState<DetailState>();
  const [loading, setLoading] = useState(true);
  const [showPasswordInput, setShowPasswordInput] = useState(false);
  const [shareUserName, setShareUserName] = useState<string>('');

  const meId = useMeStore((state) => state.me.id);
  const notInitial = meId === null;

  // 初始化
  useEffect(() => {
    // 查看文件详情
    async function viewFileDetail() {
      setLoading(true);
      const [err, res] = await to<AxiosResponse<FileDetail>, AxiosResponse<ErrorResponse>>(fileDetail(guid));
      setLoading(false);
      if (res?.status === 200) {
        const fileData = res.data;
        if (fileData.passwordProtected && !fileData.permissions.readable) {
          setShareUserName(fileData.user.name);
          setShowPasswordInput(true);
        }
        setDetail({
          ok: true,
          data: fileData,
        });
      } else if (isFillForm() || isFormResponseSharePath()) {
        // 表单填写页和表单分享回复页不对文件权限判断
        setDetail({
          ok: true,
        });
      } else {
        setDetail({
          ok: false,
          data: err?.data,
        });
      }
    }

    viewFileDetail();
  }, [guid]);

  return notInitial ? null : (
    <div className={styles.myLayout}>
      {loading ? null : detail?.ok ? (
        showPasswordInput ? (
          <PasswordInput shareUserName={shareUserName} />
        ) : (
          <>
            <EditorHeader data={detail.data} />
            <div className={styles.content} id="editor-content">
              {props.children}
            </div>
          </>
        )
      ) : (
        <ErrorPageRenderer errorCode={detail?.data?.code} />
      )}
    </div>
  );
}
