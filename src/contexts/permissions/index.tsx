import type { ReactNode } from 'react';
import React, { createContext, useEffect, useReducer } from 'react';

import { useMeStore } from '@/store/Me';

import { permissionReducer } from './reducer';
import {
  getAlphaFeature,
  getCurrentPermissions,
  getLicenseLimitType,
  getStateSyncThirdParty,
  getTeamInfo,
  getUserFeatures,
  isDingtalkOrWeWork,
} from './service/api';
import type { Action, PermissionState } from './type';
import { alphaFeatureKeys } from './type';

const initState: PermissionState = {
  alphaFeatures: {},
  features: [],
  currentPermissions: [],
  limitType: undefined,
  loaded: false,
  isDingtalk: false,
  isSSOSAML: false,
  isWework: false,
  teamInfo: null,
  syncFromThirdparty: false,
  syncPanelEnabled: false,
};

export const PermissionContext = createContext<PermissionState>(initState);
export const PermissionDispatchContext = createContext<null | React.Dispatch<Action>>(null);

export const PermissionProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(permissionReducer, initState);
  const me = useMeStore((state) => state.me);

  useEffect(() => {
    if (me?.id) {
      const { alphaFeatures } = state;
      Promise.all([
        Promise.all(alphaFeatureKeys.map(async (key) => getAlphaFeature(key).then((value) => ({ key, value })))),
        getCurrentPermissions(),
        getUserFeatures(),
        getLicenseLimitType(),
      ]).then(([alphaFeatureItem, permissionRes, featuresRes, limitTypeRes]) => {
        const alphaFeatureList = alphaFeatureItem.reduce((pre, { key, value }) => {
          return { ...pre, [key]: value };
        }, alphaFeatures);

        dispatch({
          type: 'setState',
          payload: {
            ...state,
            alphaFeatures: alphaFeatureList,
            currentPermissions: permissionRes.data.permissions,
            features: featuresRes.data.features,
            loaded: true,
            limitType: limitTypeRes?.data?.limitType,
          },
        });
      });

      getTeamInfo().then((response) => {
        if (response.id) {
          Promise.all([isDingtalkOrWeWork(response.id), getStateSyncThirdParty(response.id)]).then(
            ([dingOrWeworkRes, syncRes]) => {
              if (dingOrWeworkRes.isDingtalk !== undefined) {
                dispatch({
                  type: 'setIsDingtalkOrWework',
                  payload: {
                    ...dingOrWeworkRes,
                  },
                });
                dispatch({
                  type: 'setTeamInfo',
                  payload: {
                    teamInfo: response,
                  },
                });
              }
              if (syncRes) {
                dispatch({
                  type: 'setSyncThirdState',
                  payload: {
                    syncFromThirdparty: syncRes.data.syncFromThirdparty,
                    syncPanelEnabled: syncRes.data.syncPanelEnabled,
                  },
                });
              }
            },
          );
        }
      });
    }
  }, [me]);

  return (
    <PermissionContext.Provider value={state}>
      <PermissionDispatchContext.Provider value={dispatch}>{children}</PermissionDispatchContext.Provider>
    </PermissionContext.Provider>
  );
};
