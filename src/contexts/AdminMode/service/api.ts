// import { getSecurityPassword } from '../../../utils/passwordEncrypt/encrypt';
import { CommonApi } from '@/api/Request';

import type {
  AuthMode,
  CheckAdminModeResponse,
  CheckEmailStatusResponse,
  CheckPasswordExistsResponse,
  GetAuthKindResponse,
} from '../type';

/**
 * 检查管理员模式
 */
export const checkAdminMode: () => Promise<CheckAdminModeResponse> = async () => {
  const response = await CommonApi.get(`/auth/admin_mode/check_and_refresh`);

  if (response.data === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 退出管理员模式
 */
export const quitAdminMode = async () => {
  const response = await CommonApi.post(`/auth/admin_mode/quit`);

  if (response.status === 200) {
    return Promise.resolve(true);
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 触发验证码
 */
export const fireCaptchaCode = async ({ voice }: { voice: boolean }) => {
  const response = await CommonApi.post(`/auth/admin_mode/send_sms_code`, {
    voice,
  });

  if (response.status === 200) {
    return Promise.resolve({});
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 触发邮箱验证码
 */
export const fireEmailCode = async () => {
  const response = await CommonApi.post(`/auth/admin_mode/send_email_code`);

  if (response.status === 200) {
    return Promise.resolve({});
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 发送验证码
 */
export const sendCaptchaCode = async ({ code }: { code: string }) => {
  const response = await CommonApi.post(`/auth/admin_mode/verify_sms_code`, {
    code,
  });

  if (response.status && response.status < 300 && response.status >= 200) {
    return Promise.resolve({});
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 向绑定邮箱发送验证码
 */
export const sendEmailCaptchaCode = async ({ code }: { code: string }) => {
  const response = await CommonApi.post(`/auth/admin_mode/verify_email_code`, {
    code,
  });

  if (response.status && response.status < 300 && response.status >= 200) {
    return Promise.resolve({});
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 换绑和绑定邮箱
 */
export const changeOrBindEmail = async ({
  teamId,
  userId,
  email,
}: {
  teamId: number;
  userId: number;
  email: string;
}): Promise<object> => {
  const response = await CommonApi.post(`/teams/${teamId}/members/${userId}/update_by_admin`, {
    attribute: 'email',
    value: email,
  });

  if (response.data === 200 || response.status === 204) {
    return Promise.resolve({});
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 重置密码
 */
export const changePassword = async ({
  password,
  teamId,
  userId,
}: {
  teamId: number;
  userId: number;
  password: string;
}) => {
  try {
    const response = await CommonApi.post(`/teams/${teamId}/members/${userId}/update_by_admin`, {
      attribute: 'password',
      value: password,
    });

    if (response.status === 200 || response.status === 204) {
      return Promise.resolve({});
    } else {
      return Promise.reject(await response.data);
    }
  } catch (error: any) {
    return Promise.reject(await error.data);
  }
};

/**
 * 获取授权方式 sms email password
 */
export const getAuthKind: () => Promise<GetAuthKindResponse> = async () => {
  const response = await CommonApi.get(`/auth/admin_mode/auth_mode`);

  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 设置授权方式
 */
export const setAuthKind = async ({ authMode }: { authMode: AuthMode }) => {
  const response = await CommonApi.post(`/auth/admin_mode/auth_mode`, { authMode });

  if (response.status === 200) {
    return Promise.resolve({});
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 创建授权方式记录
 */
export const createAuthKindRecord = async ({ authMode }: { authMode: AuthMode }) => {
  const response = await CommonApi.put(`/auth/admin_mode/auth_mode`, { authMode });

  if (response.status === 200) {
    return Promise.resolve({});
  } else {
    return Promise.reject(await response);
  }
};

/**
 * 检查管理员邮箱的状态
 */
export const checkEmailStatus: () => Promise<CheckEmailStatusResponse> = async () => {
  const response = await CommonApi.get(`/auth/admin_mode/check_can_control`);

  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 检验当前管理员是否已经设置过密码
 */
export const checkPasswordExists: () => Promise<CheckPasswordExistsResponse> = async () => {
  const response = await CommonApi.get(`/auth/admin_mode/check_admin_password`);

  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 发送管理员密码，准备进入管理员模式
 */
export const sendPassword = async ({ password }: { password: string }) => {
  const response = await CommonApi.post(`/auth/admin_mode/verify_admin_password`, {
    password,
  });

  if (response.status < 300 && response.status >= 200) {
    return Promise.resolve({});
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 设置管理员模式
 */
export const setAdminPassword = async ({ password }: { password: string }) => {
  const response = await CommonApi.post(`/auth/admin_mode/admin_password`, {
    password,
  });

  if (response.status === 200) {
    return Promise.resolve({});
  } else {
    return Promise.reject(await response.data);
  }
};
