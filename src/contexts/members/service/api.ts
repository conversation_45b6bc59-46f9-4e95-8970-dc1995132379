import { random } from 'lodash';

import { CommonApi } from '@/api/Request';
import { ContentType } from '@/utils/request';

import type {
  AddMembersByAccountResponse,
  Department,
  ExternalResponse,
  InactivatedMembersResponse,
  InviteStateResponse,
  MimeMembersResponse,
  OnlyRootUserResponse,
  OuterSiderResponse,
  OutsiderFilesResponse,
  SearchUserListResponse,
  ShadowSubtreeResponse,
  UserResponse,
} from '../type';
import { baseURL, pandaURL } from './constant';

export const PAGE_SIZE = 50;
// 后端需要给加载的页面生成一个独立ID 防止搜索结果重复
const sessionIdForSearch = random(10);

/**
 * 获取部门子节点
 */
export const getShadowSubtree: (id: number) => Promise<ShadowSubtreeResponse> = async (id) => {
  const response = await CommonApi.get(`${baseURL}/org/departments/${id}/shadow_subtree`);
  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 获取部门下的用户成员
 */
export const getDepartmentUsers: (data: { departmentId: number; page: number }) => Promise<UserResponse> = async ({
  departmentId,
  page,
}) => {
  const response = await CommonApi.get(
    `${baseURL}/org/departments/${departmentId}/users?perPage=${100}&page=${page}&pageSource=contacts`,
  );
  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 加载未分配部门成员
 */
export const loadOnlyOnRootMembers: ({
  teamId,
  page,
}: {
  teamId: number;
  page: number;
}) => Promise<OnlyRootUserResponse> = async ({ teamId, page }) => {
  const response = await CommonApi.get(
    `${baseURL}/organization/${teamId}/only_on_root_users?page=${page}&limit=${PAGE_SIZE}`,
  );
  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 加载外部协作者
 */
export const loadOutsiderMembers: ({ page }: { page: number }) => Promise<OuterSiderResponse> = async ({ page }) => {
  const response = await CommonApi.get(`${baseURL}/teams/mine/outsiders/pagination?page=${page}&limit=${PAGE_SIZE}`);
  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 加载已禁用成员
 */
export const loadDisabledMembers: ({
  page,
  teamId,
}: {
  page: number;
  teamId: number;
}) => Promise<OnlyRootUserResponse> = async ({ page, teamId }) => {
  const response = await CommonApi.get(`${baseURL}/teams/${teamId}/members/disabled?page=${page}&perPage=${PAGE_SIZE}`);
  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 加载未激活成员
 */
export const loadInactivatedMembers: (data: { teamId: number }) => Promise<InactivatedMembersResponse> = async ({
  teamId,
}) => {
  const response = await CommonApi.get(`${baseURL}/teams/${teamId}/unactivated?perPage=9999`);

  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 获取「企业外部成员」或「未关联钉钉」或「未关联微信」
 */
export const loadExternal: (data: { teamId: number }) => Promise<ExternalResponse> = async ({ teamId }) => {
  const response = await CommonApi.get(`${baseURL}/teams/${teamId}/members/external`);

  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 搜索用户
 */
//@ts-ignore
export const searchUsers: (data: {
  keyword: string;
  limit?: number;
  next?: number;
}) => Promise<SearchUserListResponse> = async ({ keyword, next, limit = 100 }) => {
  const nextParams = next ? { next } : {};
  const response = await CommonApi.post(`${pandaURL}/search`, {
    keyword,
    ...nextParams,
    pageSource: 'contacts',
    sessionId: String(sessionIdForSearch),
    limit,
    filter: {
      user: {
        includeRecentContact: false,
        includeTeamMember: true,
        includeDisabledMember: true,
      },
    },
  });
  if (response.status === 200) {
    return response;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 创建子部门
 */
export const createSubDepartment: (data: { name: string; parentID: number }) => Promise<Department> = async (data) => {
  const response = await CommonApi.post(`${baseURL}/departments`, data);
  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 编辑部门名称
 */
export const editDepartment: (data: { id: number; name: string }) => Promise<any> = async ({ id, name }) => {
  const response = await CommonApi.patch(`${baseURL}/departments/${id}/rename`, {
    name,
  });

  if (response.status === 200 || response.status === 204) {
    // 这个接口正确的情况 什么都不会返回
    return Promise.resolve({});
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 删除部门
 */
export const deleteDepartment: (data: { id: number }) => Promise<any> = async ({ id }) => {
  const response = await CommonApi.delete(`${baseURL}/departments/${id}`);

  if (response.status === 200 || response.status === 204) {
    // 这个接口正确的情况 什么都不会返回
    return Promise.resolve({});
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 获取用户列表
 * @param page 这个老接口 并不支持翻页 但是依然要传递
 */
export const getMembers: (page: number, perPage: number) => Promise<MimeMembersResponse> = async (page, perPage) => {
  const response = await CommonApi.get(`${baseURL}/teams/mine/members?perPage=${perPage}&pagination=true&page=${page}`);
  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 向部门批量添加成员
 */
export const addMembersByIds = async ({ departmentIDs, userIDs }: { departmentIDs: number[]; userIDs: number[] }) => {
  const response = await CommonApi.post(`${baseURL}/departments/action/add_users_to_departments_by_id`, {
    departmentIDs,
    userIDs,
  });
  if (response.status === 200 || response.status === 204) {
    // 这个接口正确的情况 什么都不会返回
    return Promise.resolve({});
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 从部门里面批量删除成员
 */
export const deleteMembers: (data: { ids: number[]; departmentId: number }) => Promise<any> = async ({
  ids,
  departmentId,
}) => {
  const response = await CommonApi.delete(`${baseURL}/departments/${departmentId}/users`, {
    data: {
      ids,
    },
  });

  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 通过邮箱和手机号批量添加成员
 */
export const addMembersByAccount: (data: {
  departmentID: number;
  accounts: string[];
}) => Promise<AddMembersByAccountResponse> = async ({ accounts, departmentID }) => {
  const response = await CommonApi.post(`${baseURL}/departments/action/add_users_to_department_by_account`, {
    departmentID,
    accounts,
  });

  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 私部环境 添加成员
 */
export const addPDMembers = async ({ email, name, teamId }: { email: string; name: string; teamId: number }) => {
  try {
    const response = await CommonApi.post(`${baseURL}/teams/${teamId}/users`, {
      email,
      name,
    });

    if (response.status === 200) {
      return response?.data || Promise.resolve({});
    } else {
      return Promise.reject(await response);
    }
  } catch (error) {
    return Promise.reject(await error);
  }
};

/**
 * 上传文件
 */
export const uploadFile = async (file: File) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('encodedFileName', file.name);

  const response = await CommonApi.post(`${baseURL}/teams/import_users`, formData, {
    headers: {
      [ContentType]: 'multipart/form-data',
      'x-file-size': String(file.size),
      'x-file-type': file.type,
      'x-requested-with': 'XMLHttpRequest',
    },
    responseType: 'arraybuffer',
  });
  if (response.status === 200 || response.status === 201) {
    return response;
  } else {
    return Promise.reject(response);
  }
};

/**
 * 获取邀请链接
 */
export const getInviteLink: () => Promise<InviteStateResponse> = async () => {
  const response = await CommonApi.get(`${baseURL}/teams/mine/invite_state`);
  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 打开邀请链接
 */
export const openInviteLink: () => Promise<InviteStateResponse> = async () => {
  const response = await CommonApi.post(`${baseURL}/teams/mine/invite_state`);

  if (response.status === 200) {
    return response.data;
  } else {
    Promise.reject(await response.data);
  }
};

/**
 * 关闭邀请链接
 */
export const closeInviteLink: () => Promise<InviteStateResponse> = async () => {
  const response = await CommonApi.delete(`${baseURL}/teams/mine/invite_state`);

  if (response.status === 200) {
    return response.data;
  } else {
    Promise.reject(await response.data);
  }
};

/**
 * 更改昵称
 */
export const changeNickName = async ({
  teamId,
  userId,
  username,
}: {
  teamId: number;
  userId: number;
  username: string;
}) => {
  const response = await CommonApi.post(`${baseURL}/teams/${teamId}/members/${userId}/update_by_admin`, {
    attribute: 'username',
    value: username,
  });

  if (response.status === 200 || response.status === 204) {
    return Promise.resolve({});
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 解绑邮箱
 */
export const unbindEmail = async ({ teamId, userId }: { teamId: number; userId: number }) => {
  try {
    const response = await CommonApi.post(`${baseURL}/teams/${teamId}/members/${userId}/update_by_admin`, {
      attribute: 'email',
      value: '',
    });
    if (response.status === 200 || response.status === 204) {
      return Promise.resolve({});
    } else {
      return Promise.reject(await response.data);
    }
  } catch (error: any) {
    return Promise.reject(await error.data);
  }
};

// 'delete' | 'remove'

/**
 * 删除或者禁用用户 delete | remove
 */
export const deleteMember = async ({ id, action }: { id: number; action: 'delete' | 'remove' }) => {
  const response = await CommonApi.delete(
    `${baseURL}/teams/mine/members/${id}`,
    {
      data: {
        action,
      },
    },
    // {
    //   headers: {
    //     [ContentType]: 'application/x-www-form-urlencoded',
    //   }
    // },
  );

  if (response.status === 200) {
    return Promise.resolve({});
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 禁用成员 ｜ 更改成员角色  'member' | 'seat' | 'admin'
 */
export const disabledOrChangeMemberRole = async ({
  id,
  role,
  teamId,
}: {
  id: number;
  role: 'member' | 'seat' | 'admin' | 'disabled';
  teamId: number;
}) => {
  const formData = new FormData();

  formData.append('role', role);

  formData.append('teamId', String(teamId));

  const response = await CommonApi.patch(`${baseURL}/teams/mine/members/${id}`, formData, {
    headers: {
      [ContentType]: 'application/x-www-form-urlencoded',
    },
  });

  if (response.status === 200) {
    return Promise.resolve({});
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 加载外部协作者文件权限
 */
export const loadOutsiderFiles: (data: { id: number }) => Promise<OutsiderFilesResponse> = async ({ id }) => {
  const response = await CommonApi.get(`${baseURL}/teams/mine/outsiders/${id}/files`);

  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 移除外部协作者文件权限
 */
export const removeOutsiderFileRight = async ({ userId, guid }: { userId: number; guid: string }) => {
  const response = await CommonApi.delete(`${baseURL}/teams/mine/outsiders/${userId}?fileGuid=${guid}`);

  if (response.status === 200) {
    return Promise.resolve({});
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 邀请成员
 */
export const inviteMember = async ({ id }: { id: number }) => {
  const response = await CommonApi.post(`${baseURL}/departments/action/add_users_to_department_by_account`, {
    data: {
      departmentID: 1,
      userIDs: [id],
    },
  });

  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 移除外部协作者
 */
export const removeOutsider = async ({ id }: { id: number }) => {
  const response = await CommonApi.delete(`${baseURL}/teams/mine/outsiders/${id}`);

  if (response.status === 200) {
    return Promise.resolve({});
  } else {
    return Promise.reject(await response.data);
  }
};

export const getWechatNameRelationLink = async ({ teamId }: { teamId: number }) => {
  const response = await CommonApi.get(`${baseURL}/sync_thirdparty/wework/name_relation_link?team_id=${teamId}`);
  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};

/**
 * 上传微信关系列表文件
 */
export const uploadWechatNameRelationFile = async (file: File) => {
  const formData = new FormData();
  formData.append('file', file);

  const response = await CommonApi.post(`${baseURL}/sync_thirdparty/wework/upload_name_relation`, {
    data: formData,
    headers: {
      [ContentType]: 'multipart/form-data',
    },
  });

  if (response.status === 200) {
    return response;
  } else {
    return Promise.reject(response);
  }
};

export const getReminder = async (teamId: number) => {
  const response = await CommonApi.get(`${baseURL}/organization/${teamId}/reminder`);
  if (response.status === 200) {
    return response.data;
  } else {
    return Promise.reject(await response.data);
  }
};
