import type { BreadcrumbItemType, BreadcrumbSeparatorType } from 'antd/es/breadcrumb/Breadcrumb';
export enum MenuKey {
  efficiency = 'efficiency',
  members = 'members',
  audit = 'enterprise/audit',
  packages = 'packages',
  whitelist = 'whitelist',
  settings = 'settings',
  jump = 'jump',
  template = 'template',
}
export type MenuItem = {
  title: string;
  label: string;
  key: string;
  icon?: React.ReactNode;
  children?: MenuItem[];
};

/**
 * GetItem 获取当前菜单
  GetAllItem 获取当前所有层级菜单
 */
export enum GetMenuTypeEnum {
  GetAllMenuItem,
  GetMenuItem,
}
export type BreadcrumbModel = Partial<BreadcrumbItemType & BreadcrumbSeparatorType>;
