import { CheckOutlined } from '@ant-design/icons';
import { type MenuProps, message } from 'antd';

import { deleteFile, deleteRecentFile } from '@/api/File';
import deleteConfirm from '@/components/fileList/components/deleteConfirm';
import { FilePathPicker } from '@/components/Modal/FilePathPicker';
import type { DownLoadFileProps } from '@/hooks/useFileTypeDownload';
import { fileStar, openFile } from '@/utils/file';

type MenuItem = Required<MenuProps>['items'][number] & {
  hidden?: boolean;
  children?: MenuItem[];
};

export const items = ({
  record,
  renameCallback,
  reload,
  i18nText,
  meId,
  setShareVisible,
  downloadDiffFile,
}: {
  record: any;
  renameCallback: ({ visible, params }: { visible: boolean; params: { guid: string; fileName: string } }) => void;
  reload: () => void;
  i18nText: { [key: string]: string };
  meId: number | null;
  setShareVisible: (visible: boolean) => void;
  downloadDiffFile: ({ type, guid, fileName }: DownLoadFileProps) => void;
}): MenuItem[] => {
  return [
    // {
    //   label: '当前标签页打开',
    //   key: '1',
    // },
    {
      label: i18nText.newTabOpens,
      key: 'newTabOpens',
      onClick: () => {
        const newArg = record;
        if (record.isShortcut) {
          const { url, type } = record.shortcutSource;
          newArg.type = type;
          newArg.url = url;
        }
        openFile({ type: newArg.type, guid: newArg.guid, url: newArg.url, model: 'new' });
      },
    },
    // {
    //   label: '预览',
    //   key: '3',
    // },
    // {
    //   label: '定位到所在文件夹',
    //   key: '4',
    // },
    // {
    //   label: '添加到快速访问',
    //   key: '5',
    // },
    {
      label: record.starred ? i18nText.removeStr : i18nText.star,
      key: 'star',
      onClick: () =>
        fileStar({ guid: record.guid, status: record.starred })
          .then(({ type }) => {
            if (type === 'star') {
              message.success(i18nText.starSuccess);
            } else {
              message.success(i18nText.removeStarSuccess);
            }
            reload();
          })
          .catch(({ type }) => {
            if (type === 'star') {
              message.error(i18nText.starError);
            } else {
              message.error(i18nText.removeStarError);
            }
          }),
    },
    {
      type: 'divider',
    },
    {
      label: i18nText.share + i18nText.collaboration,
      key: 'share',
      onClick: () => {
        setShareVisible(true);
      },
    },
    {
      type: 'divider',
    },
    {
      label: i18nText.download,
      key: 'downloadOther',
      hidden: !record.name?.includes('.') || record.type === 'folder',
      onClick: ({ key }) => {
        downloadDiffFile({ type: key, guid: record.guid, fileName: record.name });
      },
    },
    {
      label: i18nText.download,
      key: 'download',
      hidden: ['board', 'form', 'table', 'folder', 'shortcut', 'zip'].includes(record.type),
      children: [
        {
          label: i18nText.png,
          key: 'jpg',
          hidden: !['mindmap', 'newdoc'].includes(record.type) || ['img'].includes(record.type), // mindmap
        },
        {
          label: 'Xmind',
          key: 'xmind',
          hidden: !['mindmap'].includes(record.type) || ['img'].includes(record.type), // mindmap
        },
        {
          label: 'PPTX',
          key: 'pptx',
          hidden: !['presentation'].includes(record.type) || ['img'].includes(record.type), // presentation
        },
        {
          label: 'PDF',
          key: 'pdf',
          hidden: !['presentation', 'newdoc', 'modoc'].includes(record.type) || ['img'].includes(record.type), //presentation
        },
        {
          label: 'Excel',
          key: 'xlsx',
          hidden: !['mosheet'].includes(record.type) || ['img'].includes(record.type), // mosheet
        },
        {
          label: 'WPS',
          key: 'wps',
          hidden: !['modoc'].includes(record.type) || ['img'].includes(record.type), // modoc
        },
        {
          label: 'Word',
          key: 'docx',
          hidden: !['modoc', 'newdoc'].includes(record.type) || ['img'].includes(record.type), // modoc
        },
        {
          label: 'MarkDown',
          key: 'md',
          hidden: !['newdoc'].includes(record.type) || ['img'].includes(record.type), // newdoc
        },
      ],
      onClick: ({ key }) => {
        downloadDiffFile({ type: key, guid: record.guid, fileName: record.name });
      },
    },
    {
      label: i18nText.reName,
      key: 'reName',
      onClick: () => renameCallback({ visible: true, params: { fileName: record.name, guid: record.guid } }),
    },
    {
      label: i18nText.moveTo,
      key: 'moveTo',
      onClick: () => {
        FilePathPicker({
          type: 'move',
          locationGuid: 'desktop',
          source: [
            {
              name: record.name,
              fileGuid: record.guid,
              isAdmin: record.isAdmin || record.isFileAdmin,
              role: record.role,
            },
          ],
          onOk: () => {
            reload();
          },
        });
      },
    },
    {
      label: i18nText.copyTo,
      key: 'copyTo',
      onClick: () => {
        FilePathPicker({
          type: 'create',
          locationGuid: 'desktop',
          source: [
            {
              name: record.name,
              fileGuid: record.guid,
              isAdmin: record.isAdmin || record.isFileAdmin,
              role: record.role,
            },
          ],
          onOk: () => {
            reload();
          },
        });
      },
    },
    {
      type: 'divider',
    },
    {
      label: i18nText.clearRecord,
      key: 'clearRecord',
      onClick: () => {
        deleteRecentFile(record['guid'])
          .then(() => {
            message.success('记录清除成功');
            reload();
          })
          .catch((err) => {
            message.error(err);
          });
      },
    },
    {
      label: i18nText.delete,
      key: 'delete',
      danger: true,
      disabled: meId !== record.userId, // 非创建人无法删除
      onClick: () => deleteConfirm({ i18nText, data: record['guid'], api: deleteFile, callback: reload }),
    },
  ];
};

export const dropdownItems = (init: string, i18nText: { [key: string]: string }): MenuProps['items'] => [
  {
    label: i18nText.clearFilter,
    key: 'clear',
  },
  {
    label: i18nText.recentlyOpened,
    key: 'open',
    icon: init === 'open' ? <CheckOutlined /> : <span />,
  },
  {
    label: i18nText.recentlyEdit,
    key: 'edit',
    icon: init === 'edit' ? <CheckOutlined /> : <span />,
  },
];
