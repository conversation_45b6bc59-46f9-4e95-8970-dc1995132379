import { message, Spin } from 'antd';
import { useEffect, useState } from 'react';

import * as EnterpriseApi from '@/api/EnterpriseSettings';
import * as MeApi from '@/api/Me';
import { to } from '@/api/Request';
import { fm2 } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

import Template from '../Template';
import Trash from '../Trash';
import Auth from './Auth';
import css from './index.less';
import Main from './Main';
import type { AuthStatus, Capacity, CurrentView, EnterpriseInfo, Settings } from './types';

export default function EnterpriseSettings() {
  const me = useMeStore((state) => state.me);
  const [currentView, setCurrentView] = useState<CurrentView>('main');
  const [loading, setLoading] = useState(true);
  const [info, setInfo] = useState({} as EnterpriseInfo);
  const [capacity, setCapacity] = useState<Capacity | null>(null);
  const [settings, setSettings] = useState<Settings>({ watermark: false });
  const [updating, setUpdating] = useState(false);
  const [authStatus, setAuthStatus] = useState<AuthStatus | null>(null);

  async function getInfo() {
    const [, res] = await to(EnterpriseApi.info(me.teamId as number));
    setLoading(false);
    if (res?.status === 200) {
      setInfo(res.data);
    }
  }

  async function getAuthStatus() {
    const [, res] = await to(EnterpriseApi.license());
    if (res?.status === 200) {
      setAuthStatus(res.data);
    }
  }

  async function getCapacity() {
    const [, res] = await to(MeApi.quota());
    if (res?.status === 200) {
      const { teamDiskVolume } = res.data;
      setCapacity({
        total: teamDiskVolume.quota,
        used: teamDiskVolume.used,
      });
    }
  }

  async function getSettings() {
    const [, res] = await to(EnterpriseApi.settings('watermark'));
    if (res?.status === 200) {
      setSettings({ watermark: res.data.status });
    }
  }

  async function onSettingChange(setting: Partial<Settings>) {
    // 防止频繁点击
    if (updating) return;
    setUpdating(true);
    const [err, res] = await to(EnterpriseApi.updateSettings('watermark'));
    setUpdating(false);
    if (res?.status !== 204) return message.error(err?.data?.msg);
    const tip = setting.watermark ? fm2('Enterprise.watermarkOn') : fm2('Enterprise.watermarkOff');
    message.success(tip);
    setSettings((prev) => ({ ...prev, ...setting }) as Settings);
  }

  function onLogoChange(url: string) {
    setInfo({ ...info, avatar: url });
  }

  useEffect(() => {
    getInfo();
    getCapacity();
    getSettings();
    getAuthStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // 只在组件挂载时执行一次，无需依赖任何变量

  // 返回主页面
  function back() {
    setCurrentView('main');
  }

  const viewTitleMap = {
    auth: fm2('Enterprise.auth'),
    template: fm2('Enterprise.template'),
    recycleBin: fm2('Enterprise.recycleBin'),
  };

  function renderHeader() {
    if (currentView === 'main') {
      return <div className={css.header}>{fm2('Enterprise.settings')}</div>;
    }
    return (
      <div className={css.authHeader}>
        <div className={css.authTitle} onClick={back}>
          {fm2('Enterprise.settings')}
        </div>
        <div className={css.authInfo}>
          <span className={css.authLabel}>{viewTitleMap[currentView]}</span>
          <span className={css.authDesc}>
            {info.name} / {fm2('Enterprise.id')} {info.id}
          </span>
        </div>
      </div>
    );
  }

  function renderContent() {
    switch (currentView) {
      case 'auth':
        return <Auth authStatus={authStatus} />;
      case 'template':
        return <Template />;
      case 'recycleBin':
        return <Trash />;
      default:
        return (
          <Main
            capacity={capacity}
            info={info}
            setCurrentView={setCurrentView}
            settings={settings}
            onLogoChange={onLogoChange}
            onSettingChange={onSettingChange}
          />
        );
    }
  }

  return (
    <div className={css.container}>
      {renderHeader()}

      <div className={css.content}>
        <Spin spinning={loading}>{renderContent()}</Spin>
      </div>
    </div>
  );
}
