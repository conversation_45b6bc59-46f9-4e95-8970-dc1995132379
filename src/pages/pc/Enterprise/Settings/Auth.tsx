import 'dayjs/locale/zh-cn';
import 'dayjs/locale/en';

import { fm2 } from '@/modules/Locale';
import { stringifyAbsoluteDateOnly } from '@/utils/time';

import css from './index.less';
import type { AuthStatus } from './types';

interface Props {
  authStatus: AuthStatus | null;
}

export default function Auth({ authStatus }: Props) {
  if (!authStatus) return null;
  const isValid = authStatus.validUntil > Date.now();

  const validPeriod = `${stringifyAbsoluteDateOnly(authStatus.validFrom)} - ${stringifyAbsoluteDateOnly(authStatus.validUntil)}`;

  return (
    <div className={css.authContainer}>
      <div className={css.section}>
        {/* 席位数 */}
        <div className={css.row}>
          <div>
            <div className={css.label}>{fm2('Enterprise.seatNum')}</div>
            <div className={css.desc}>{fm2('Enterprise.contactBusiness')} 400-666-9920</div>
          </div>

          <div className={css.label}>
            {authStatus.memberLimit}
            {fm2('Enterprise.seatNumDesc', { userCount: authStatus.userCount })}
          </div>
        </div>

        <div className={css.divider} />

        {/* 服务状态 */}
        <div>
          <div className={css.label}>{fm2('Enterprise.serviceStatus')}</div>
          <div className={css.desc}>
            {isValid ? fm2('Enterprise.serviceStatusValid') : fm2('Enterprise.serviceStatusExpired')}
          </div>
        </div>

        <div className={css.divider} />

        {/* 有效期 */}
        <div>
          <div className={css.smallLabel}>{fm2('Enterprise.validityPeriod')}</div>
          <div className={css.desc}>{validPeriod}</div>
        </div>
      </div>
    </div>
  );
}
