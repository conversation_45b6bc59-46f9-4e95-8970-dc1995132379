.content {
  height: 100%;
  position: relative;
  box-sizing: border-box;
  background-color: var(--theme-layout-color-bg-new-page);

  .filterTop {
    padding: 20px;
    margin-bottom: 16px;
    border-radius: 8px;
    border: 1px solid var(--theme-basic-color-lighter);
    background: var(--theme-basic-color-bg-default);
    display: flex;
    justify-content: space-between;

    .selectIcon {
      display: flex;
      flex-direction: row;
      gap: 8px;
    }

    :global {
      .ant-form-item {
        margin-bottom: 0;
      }

      .ant-btn {
        padding: 0 29px;
      }
    }
  }

  .templateList {
    border-radius: 8px;
    overflow: hidden;
    background-color: var(--theme-basic-color-bg-default);
    border: 1px solid var(--theme-basic-color-lighter);
    height: calc(100% - 90px);
    display: flex;
    align-items: center;
    justify-content: center;

    .desc {
      color: var(--theme-text-color-default);
      font-size: 14px;
      line-height: 24px;
    }

    :global {
      .ant-table-wrapper {
        width: 100%;
        height: 100%;

        .ant-table-header {
          .ant-table-cell {
            border-bottom: 1px solid var(--theme-basic-color-lighter);
            padding: 16px;
            background-color: var(--theme-card-default);
          }
        }

        .ant-table-tbody {
          .ant-table-tbody-virtual-holder-inner {
            background-color: var(--theme-basic-color-bg-default);

            .ant-table-row {
              &:hover {
                .ant-table-cell {
                  background-color: var(--theme-menu-color-bg-hover);
                }
              }

              .ant-table-cell {
                padding: 8px 16px;
                background-color: var(--theme-basic-color-bg-default);
                color: var(--theme-text-color-default);
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                display: flex;
                align-items: center;
              }
            }
          }
        }
      }

      .ant-empty-description {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        color: var(--theme-text-color-default);
        font-size: 28px;
        font-weight: 500;
        line-height: 48px;

        .ant-btn {
          padding: 0;
          font-size: 28px;
          font-weight: 500;
          line-height: 48px;
        }
      }
    }
  }
}

.action {
  display: flex;

  :global {
    .ant-btn {
      padding: 0;

      & + .ant-btn {
        margin-left: 8px;
      }
    }
  }
}
