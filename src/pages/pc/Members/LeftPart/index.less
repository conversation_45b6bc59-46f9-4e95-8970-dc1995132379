.styledContent {
  border-radius: 8px;
  border: 1px solid var(--theme-separator-color-lighter);
  height: 100%;
  display: flex;
}

.styledLeft {
  width: 300px;
  box-sizing: border-box;
  border-right: 1px solid var(--theme-separator-color-lighter);
  display: flex;
  flex-direction: column;
}

.styledSearchContainer {
  padding: 16px;
  border-bottom: 1px solid var(--theme-separator-color-lighter);
}

.treeListContainer {
  flex: 1;
  overflow: auto;
  padding: 8px 0;
  display: flex;
  flex-direction: column;

  .ant-tree {
    background: transparent;
    padding-left: 8px;
    flex: 1;
    height: 0;
    overflow-y: auto;

    .ant-tree-switcher {
      display: flex;
      align-items: center;
      justify-content: center;

      &::before {
        height: 100%;
      }
    }

    .ant-tree-node-content-wrapper {
      padding: 0;
    }
  }
}

.normaleMemberList {
  font-size: 12px;
}

.plusDepartment {
  display: flex;
  width: 300px;
  padding: 16px 16px 24px;
  flex-direction: column;
  align-items: flex-start;
  border-top: 1px solid var(--theme-separator-color-lighter);
}

.styledOtherList {
  padding: 8px 0;
  border-top: 1px solid var(--theme-separator-color-lighter);

  &.hasBottomBorder {
    border-bottom: 1px solid var(--theme-separator-color-lighter);
  }
}

.otherItem {
  padding: 8px 16px;
  height: 40px;
  overflow: hidden;
  color: var(--theme-text-color-default);
  text-overflow: ellipsis;
  font-size: 13px;
  line-height: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;

  &:hover {
    background: var(--theme-menu-color-bg-hover);
  }

  &.active {
    background: var(--theme-menu-color-bg-active);
  }
}

.styledTitle {
  font-size: 14px;
  line-height: 24px;
}

.styledPlusButton {
  width: 100%;
  height: auto;
  padding: 8px 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.styledDeleteText {
  color: var(--theme-text-color-alert);
}

.styledModalContentText {
  color: var(--theme-text-color-default);
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 12px;
}

.externalItem {
  display: flex;
  align-items: center;
  padding-left: 12px;
  cursor: pointer;
  height: 40px;
  margin-bottom: 8px;
}

.externalLeft {
  display: flex;
  height: 100%;
  flex: 1;
  align-items: center;
  margin-left: 6px;

  &:hover {
    background: var(--theme-menu-color-bg-hover);
  }

  &.active {
    background: var(--theme-menu-color-bg-active);
  }
}
