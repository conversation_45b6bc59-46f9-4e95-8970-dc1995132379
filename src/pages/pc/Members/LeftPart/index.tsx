import s18n from '@shimo/simple-i18n';
import { Button, Input, Tooltip } from 'antd';
import classNames from 'classnames';
import React, { useContext, useState } from 'react';

import { ReactComponent as PlusIconSVG } from '@/assets/images/members/plus-icon.svg';
// import { ReactComponent as SearchSvg } from '@/assets/images/svg/search.svg';
import { useMeStore } from '@/store/Me';

import { MembersContext, MembersDispatchContext } from '../../../../contexts/members';
import { type ItemSource, OtherListKeys } from '../../../../contexts/members/type';
import { PermissionContext } from '../../../../contexts/permissions';
import { getManageTeamMembers } from '../../../../contexts/permissions/helper';
import { AddSubDepartmentModal } from '../components/AddSubDepartment ';
import { CantDeleteModal } from '../components/CantDeleteModal';
import { DeleteDepartmentModal } from '../components/DeleteDepartment';
import { EditDepartmentNameModal } from '../components/EditDepartmentNameModal';
import { MaxLevel } from '../const';
import { getDepartmentById, isChildOfTarget } from '../utils';
import { ExternalItem } from './components/ExternalItem';
import { OtherList } from './components/OtherList';
import { TreeList } from './components/TreeList';
import styles from './index.less';

interface Props {
  changeActiveDepartmentId: (v: number) => void;
  activeDepartmentId: number;
}

export const LeftPart: React.FC<Props> = ({ changeActiveDepartmentId, activeDepartmentId }) => {
  const { activeOtherKey, unactivated, treeDataSource } = useContext(MembersContext);
  const { isDingtalk, isSSOSAML, isWework, syncFromThirdparty, features, currentPermissions } =
    useContext(PermissionContext);
  const {
    dispatch,
    getOnlyOnRootMembers,
    getOutsiderMembers,
    getDisabledMembers,
    getSearchUserList,
    getInactivatedList,
  } = useContext(MembersDispatchContext);
  const me = useMeStore((state) => state.me);

  const [addSubDepartmentOpen, setAddSubDepartmentOpen] = useState(false);
  const [editNameOpen, setEditNameOpen] = useState(false);
  const [deleteDepartmentOpen, setDeleteDepartmentOpen] = useState(false);

  const [cantDeleteOpen, setCantDeleteOpen] = useState(false);
  const [currentDepartmentLevel, setCurrentDepartmentLevel] = useState(0);
  const [currentDepartment, setCurrentDepartment] = useState<ItemSource | null>(null);
  const menageTeamMemberPermission = getManageTeamMembers({
    manageTeamMembers: currentPermissions.includes('manage_team_members'),
  });
  const manageOrgPermission = features.includes('organization_management');
  const canManageTeamMember = menageTeamMemberPermission && manageOrgPermission;
  const treeContainerRef = React.useRef<HTMLDivElement>(null);

  const handleOtherItem = async (id: OtherListKeys) => {
    dispatch?.({
      type: 'setIsInSearchMode',
      payload: {
        isInSearchMode: false,
      },
    });
    dispatch?.({
      type: 'setActiveOtherKey',
      payload: {
        activeOtherKey: id,
      },
    });
    if (me?.id) {
      if (id === OtherListKeys.OnlyOnRoot) {
        // 未分配部门成员
        await getOnlyOnRootMembers?.({ teamId: me.teamId, page: 1 });
      } else if (id === OtherListKeys.OuterSider) {
        // 外部写作者
        await getOutsiderMembers?.({ page: 1 });
      } else if (id === OtherListKeys.Disabled) {
        // 已禁用成员
        await getDisabledMembers?.({ teamId: me.teamId, page: 1 });
      } else if (id === OtherListKeys.Inactivated) {
        await getInactivatedList?.(me.teamId);
      }
    }
  };

  const handleSearch = (keyword: string) => {
    getSearchUserList?.(keyword);
  };

  const getThirdPartyName = () => {
    if (isDingtalk) {
      return s18n('钉钉');
    } else if (isSSOSAML) {
      return s18n('身份提供商');
    } else if (isWework) {
      return s18n('企业微信');
    } else {
      return '';
    }
  };

  const isThird = isDingtalk || isSSOSAML || isWework || syncFromThirdparty;
  const showPlusButton = () => {
    const currentNodeLevel = currentDepartmentLevel;

    if (canManageTeamMember && currentNodeLevel < MaxLevel) {
      return true;
    }

    return false;
  };

  const changeTreeFocus = (department: ItemSource, parentId: number) => {
    if (
      activeDepartmentId === department.id ||
      isChildOfTarget({
        selectedId: activeDepartmentId,
        targetNode: department,
      })
    ) {
      //  a. 删除的节点是否是当前选中的节点
      //  b. 删除的节点是否包含当前选中的节点
      // 如果是：则将焦点移动到被删除节点的父节点
      changeActiveDepartmentId(parentId);
    }
  };

  return (
    <div className={styles['styledLeft']}>
      <div className={styles['styledSearchContainer']}>
        <Input.Search
          allowClear
          enterButton={false}
          placeholder={s18n('搜索成员')}
          // prefix={<SearchSvg />}
          onSearch={handleSearch}
        />
      </div>
      <div ref={treeContainerRef} className={styles['treeListContainer']}>
        <TreeList
          activeDepartmentId={activeDepartmentId}
          changeActiveDepartmentId={changeActiveDepartmentId}
          setAddSubDepartmentOpen={setAddSubDepartmentOpen}
          setCantDeleteOpen={setCantDeleteOpen}
          setCurrentDepartment={setCurrentDepartment}
          setCurrentDepartmentLevel={setCurrentDepartmentLevel}
          setDeleteDepartmentOpen={setDeleteDepartmentOpen}
          setEditNameOpen={setEditNameOpen}
          treeContainerRef={treeContainerRef}
        />
        <ExternalItem />
        <OtherList />
        {unactivated?.length > 0 && (
          <div style={{ paddingTop: 8 }}>
            <div
              key={OtherListKeys.Inactivated}
              className={classNames({
                active: OtherListKeys.Inactivated === activeOtherKey,
                OtherItem: 'OtherItem',
              })}
              onClick={() => {
                handleOtherItem(OtherListKeys.Inactivated);
              }}
            >
              {s18n('未激活成员')}
            </div>
          </div>
        )}
      </div>
      {showPlusButton() && (
        <div className={styles['plusDepartment']}>
          <Tooltip title={isThird && getThirdPartyName() ? s18n`请在${getThirdPartyName()}通讯录中管理成员和部门` : ''}>
            <Button
              className={styles['styledPlusButton']}
              disabled={isThird || !!activeOtherKey}
              onClick={() => {
                const curDepartment = getDepartmentById(treeDataSource, activeDepartmentId);
                setCurrentDepartment(curDepartment);
                setAddSubDepartmentOpen(true);
              }}
            >
              <PlusIconSVG />
              <div className={styles['styledTitle']}>{s18n('添加子部门')}</div>
            </Button>
          </Tooltip>
        </div>
      )}
      {addSubDepartmentOpen && (
        <AddSubDepartmentModal
          changeOpen={(v: boolean) => {
            setAddSubDepartmentOpen(v);
          }}
          open={addSubDepartmentOpen}
          parentDepartment={currentDepartment}
        />
      )}
      {editNameOpen && (
        <EditDepartmentNameModal
          changeOpen={(v: boolean) => {
            setEditNameOpen(v);
          }}
          department={currentDepartment}
          open={editNameOpen}
        />
      )}
      {deleteDepartmentOpen && currentDepartment && (
        <DeleteDepartmentModal
          changeOpen={(v: boolean) => {
            setDeleteDepartmentOpen(v);
          }}
          changeTreeFocus={changeTreeFocus}
          department={currentDepartment}
          open={deleteDepartmentOpen}
        />
      )}
      <CantDeleteModal
        changeOpen={(v: boolean) => {
          setCantDeleteOpen(v);
        }}
        open={cantDeleteOpen}
      />
    </div>
  );
};
