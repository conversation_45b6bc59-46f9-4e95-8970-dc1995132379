import s18n from '@shimo/simple-i18n';
import { message } from 'antd';
import React, { useContext, useState } from 'react';

import { CustomModal } from '@/components/CustomModal';
import { MembersContext, MembersDispatchContext } from '@/contexts/members';
import { removeOutsider } from '@/contexts/members/service/api';
import type { UserItem } from '@/contexts/members/type';

import styles from './index.less';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  user: UserItem | null;
}

export const RemoveOutersModal: React.FC<Props> = ({ open, changeOpen, user }) => {
  const [loading, setLoading] = useState(false);
  const { outsiderPage } = useContext(MembersContext);
  const { getOutsiderMembers } = useContext(MembersDispatchContext);

  const handleOk = async () => {
    if (!user) {
      return;
    }

    setLoading(true);
    try {
      // 1 发送移除请求
      await removeOutsider({ id: user.id });
      // 2 刷新列表数据
      await getOutsiderMembers?.({ page: outsiderPage });
      // 3 成功操作提示
      message.success(s18n('操作成功'));
      // 4 隐藏窗口
      changeOpen(false);
    } catch (error) {
      const errorMsg = (error as any).error || s18n('未知错误');
      message.error(errorMsg);
    }
    setLoading(false);
  };

  const handleCancel = () => {
    changeOpen(false);
  };

  return (
    <CustomModal
      centered
      closable
      footer={(_, { CancelBtn, OkBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      }}
      maskClosable={false}
      modalType="warning"
      okButtonProps={{
        loading,
        danger: true,
      }}
      okText={s18n('确定移除')}
      open={open}
      title={s18n('移除成员')}
      width={420}
      onCancel={handleCancel}
      onOk={handleOk}
    >
      <div className={styles['styledContentText']}>{s18n.x`确定移除成员"${user?.name || ''}"`}</div>
    </CustomModal>
  );
};
