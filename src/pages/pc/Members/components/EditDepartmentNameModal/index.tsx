import s18n from '@shimo/simple-i18n';
import { Form, Input, message, Modal } from 'antd';
import React, { useContext, useState } from 'react';

import { MembersDispatchContext } from '@/contexts/members';
import { editDepartment } from '@/contexts/members/service/api';
import type { Department } from '@/contexts/members/type';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  department: Department | null;
}

const { Item } = Form;
const DepartmentName = 'departmentName';

export const EditDepartmentNameModal: React.FC<Props> = ({ open, changeOpen, department }) => {
  const [form] = Form.useForm();
  const { getSubDepartmentList } = useContext(MembersDispatchContext);
  const [loading, setLoading] = useState(false);

  React.useEffect(() => {
    if (department) {
      form.setFieldsValue({
        [DepartmentName]: department.name,
      });
    }
  }, [department]);

  if (!department) {
    return null;
  }

  const handleOk = async () => {
    setLoading(true);
    try {
      const validateData = await form.validateFields();
      // 更改名称
      await editDepartment({
        id: department.id,
        name: validateData[DepartmentName],
      });
      // 更新树状图数据
      await getSubDepartmentList?.(department.id);
      changeOpen(false);
      message.success(s18n`操作成功`);
    } catch (error: any) {
      message.error(error.data.msg || `操作成功`);
    }
    setLoading(false);
  };

  return (
    <Modal
      centered
      closable
      footer={(_, { OkBtn, CancelBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      }}
      maskClosable={false}
      okButtonProps={{
        loading,
      }}
      open={open}
      title={s18n('修改部门名称')}
      width={420}
      onCancel={() => {
        changeOpen(false);
      }}
      onOk={handleOk}
    >
      <Form form={form} layout="vertical" requiredMark={false} validateTrigger={['onBlur']}>
        <Item
          label={s18n('部门名称')}
          name={DepartmentName}
          rules={[
            {
              required: true,
              message: s18n('请先输入部门名称'),
            },
            {
              pattern: /^[\S\s]+$/g,
              message: s18n('部门名称最少一个字或字符'),
            },
            {
              max: 50,
              message: s18n('部门名称最长 50 个字'),
            },
          ]}
        >
          <Input />
        </Item>
      </Form>
    </Modal>
  );
};
