import s18n from '@shimo/simple-i18n';
import { Form, Input, message, Modal } from 'antd';
import React, { useContext, useState } from 'react';

import { AdminModeDispatchContext } from '@/contexts/AdminMode';
import { changePassword } from '@/contexts/AdminMode/service/api';
import type { ChangeOrBindEmailErrorResponse } from '@/contexts/AdminMode/type';
import type { UserItem } from '@/contexts/members/type';
import { getSecurityPassword } from '@/modules/encrypt';
import { getPasswordError } from '@/utils/validate';

const { Item } = Form;
const { Password } = Input;

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  currentTableUser: UserItem | null;
}

const PasswordText = 'password';

export const RestPasswordModal: React.FC<Props> = ({ open, changeOpen, currentTableUser }) => {
  const [form] = Form.useForm();
  const [passwordValue, setPasswordValue] = useState('');
  const { openAuthModal } = useContext(AdminModeDispatchContext);
  const [loading, setLoading] = useState(false);

  if (!currentTableUser) {
    return null;
  }

  const handleCancel = () => {
    changeOpen(false);
  };

  const changePasswordValue = async ({ password }: { password: string }) => {
    try {
      return await changePassword({
        password,
        teamId: currentTableUser.teamId,
        userId: currentTableUser.id,
      });
    } catch (error) {
      return Promise.reject(error);
    }
  };

  const handleOK = async () => {
    setLoading(true);
    let password = '';
    try {
      const formResult = await form.validateFields();
      password = formResult[PasswordText];
      const encryptedPassword = getSecurityPassword(password);

      await changePasswordValue({ password: encryptedPassword });
      form.resetFields();
      message.success(s18n('操作成功'));
      changeOpen(false);
    } catch (error) {
      const errorCode = (error as ChangeOrBindEmailErrorResponse)?.code;
      const errorMsg = (error as ChangeOrBindEmailErrorResponse)?.msg;
      if (errorCode === 17004) {
        // 管控模式授权失败
        // 启动特殊授权模式
        openAuthModal?.({
          onSuccess: () => {
            changePasswordValue({ password }).finally();
          },
        });
        // 隐藏 当前绑定对话框
        changeOpen(false);
        return;
      }

      if (errorMsg) {
        message.error(errorMsg);
      }
    }
    setLoading(false);
  };

  const handlePasswordChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    setPasswordValue(e.target.value);
  };

  return (
    <Modal
      centered
      closable
      cancelText={s18n('暂不修改')}
      footer={(_, { CancelBtn, OkBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      }}
      maskClosable={false}
      okButtonProps={{
        disabled: !passwordValue,
        loading,
      }}
      okText={s18n('提交')}
      open={open}
      title={s18n('重置密码')}
      width={420}
      onCancel={handleCancel}
      onOk={handleOK}
    >
      <Form form={form} layout="vertical" requiredMark={false} validateTrigger={['onBlur']}>
        <Item
          label={s18n('新密码')}
          name={PasswordText}
          rules={[
            {
              validator: (_, value) => {
                const text = getPasswordError(value);
                if (!text) {
                  return Promise.resolve();
                } else {
                  return Promise.reject(text);
                }
              },
            },
          ]}
        >
          <Password onChange={handlePasswordChange} />
        </Item>
      </Form>
    </Modal>
  );
};
