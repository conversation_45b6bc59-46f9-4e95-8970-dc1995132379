import s18n from '@shimo/simple-i18n';
import { <PERSON>ton, Modal } from 'antd';
import React, { useContext, useEffect, useState } from 'react';

import { ActiveRangeContext, ActiveRangeProvider } from '@/contexts/activeRange';
import { saveThirdSelectedScope } from '@/contexts/activeRange/service/api';
import { PermissionContext } from '@/contexts/permissions';
import { useMeStore } from '@/store/Me';

import { StepOne } from './components/stepOne';
import { ActiveRangeSuccess } from './components/stepThree';
import { StepTwo } from './components/stepTwo';
import styles from './index.less';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  activeDepartmentId: number;
}

enum Steps {
  Step1 = 'Step1',
  Step2 = 'Step2',
  Step3 = 'Step3',
}

const StartSecond = 5;

const ActiveRangeModal: React.FC<Props> = ({ open, changeOpen, activeDepartmentId }) => {
  const { getThirdCountSeatLoading, selectedSeatCount, unselectedCreator, unselectedCurrentUser } =
    useContext(ActiveRangeContext);
  const { teamInfo } = useContext(PermissionContext);
  const me = useMeStore((state) => state.me);

  const { selectedDepartments, selectedUsers, selectedBlacklist } = useContext(ActiveRangeContext);
  const total = teamInfo?.membership?.memberCount || 0;
  const hasExceed = selectedSeatCount > 0 && selectedSeatCount > total;
  const [step, setStep] = useState<Steps>(Steps.Step1);
  const [leftSecond, setLeftSecond] = useState(StartSecond);
  const [loading, setLoading] = useState(false);
  const [activeUserCount, setActiveUserCount] = useState(0);

  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (step === Steps.Step2) {
      timer = setInterval(() => {
        setLeftSecond((prev) => {
          if (prev - 1 <= 0) {
            clearInterval(timer);
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      setLeftSecond(StartSecond);
      clearInterval(timer!);
    }
  }, [step]);

  const handleCancel = () => {
    setStep(Steps.Step1);
    changeOpen(false);
  };

  const handleOK = async () => {
    if (step === Steps.Step1) {
      setStep(Steps.Step2);
    } else if (step === Steps.Step2) {
      setLoading(true);
      // 向后端发送变更请求
      await saveThirdSelectedScope(me!.teamId, {
        departmentIDs: selectedDepartments.map((item) => item.id),
        userIDs: selectedUsers.map((item) => item.id),
        userBlacklist: selectedBlacklist.map((item) => item.id),
      });
      setLoading(false);
      setStep(Steps.Step3);
    }
  };

  const getOkText = () => {
    if (step === Steps.Step1) {
      return s18n('下一步');
    }
    const okText =
      leftSecond > 0
        ? `
  ${s18n('确定')}（${leftSecond}s）`
        : s18n('确定');
    return okText;
  };

  const ContentText =
    step === Steps.Step1
      ? s18n`「设置激活范围」会从第三方系统实时同步企业组织架构到企业管理后台，并根据组织架构中人员和部门的选中状态更新其在中的「激活 / 禁用」状态。`
      : step === Steps.Step2
        ? s18n`本次操作总共将激活 ${activeUserCount} 位成员，请确认以下信息，操作确认后将不可撤回。`
        : null;

  return (
    <Modal
      centered
      closable={step !== Steps.Step3}
      footer={(_, { OkBtn, CancelBtn }) => {
        if (step === Steps.Step3) {
          return null;
        }
        if (step === Steps.Step1) {
          return (
            <>
              <OkBtn />
              <CancelBtn />
            </>
          );
        }
        return (
          <div className={styles['styledFooterContainer']}>
            <Button
              onClick={() => {
                setStep(Steps.Step1);
              }}
            >
              {s18n('上一步')}
            </Button>
            <div>
              <OkBtn />
              <CancelBtn />
            </div>
          </div>
        );
      }}
      maskClosable={false}
      okButtonProps={{
        style: {
          marginRight: 8,
        },
        loading,
        disabled:
          (step === Steps.Step2 && leftSecond > 0) ||
          getThirdCountSeatLoading ||
          hasExceed ||
          !!unselectedCreator ||
          !!unselectedCurrentUser,
      }}
      okText={getOkText()}
      open={open}
      title={step !== Steps.Step3 && s18n('设置激活范围')}
      width={step === Steps.Step3 ? 600 : 750}
      onCancel={handleCancel}
      onOk={handleOK}
    >
      {step === Steps.Step3 && (
        <ActiveRangeSuccess
          activeDepartmentId={activeDepartmentId}
          handleCancel={handleCancel}
          successNum={selectedSeatCount}
        />
      )}
      {(step === Steps.Step1 || step === Steps.Step2) && (
        <>
          <div className={styles['styledContentInfo']}>{ContentText}</div>
          {step === Steps.Step1 && <StepOne />}
          {step === Steps.Step2 && <StepTwo setActiveUserCount={setActiveUserCount} />}
        </>
      )}
    </Modal>
  );
};

export const ActiveRangeModalWithProvider: React.FC<Props> = (props) => {
  return (
    <ActiveRangeProvider>
      <ActiveRangeModal {...props} />
    </ActiveRangeProvider>
  );
};
