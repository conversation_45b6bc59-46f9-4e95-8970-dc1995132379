import { Avatar } from 'antd';
import React from 'react';

import AvatarIconPNG from '@/assets/images/members/avatar-icon.png';
import type { ThirdUser } from '@/contexts/activeRange/type';
import { concatImageUrl } from '@/utils/image';

import styles from './index.less';

interface Props {
  list: ThirdUser[];
  loading: boolean;
}

export const StepTwoUserList: React.FC<Props> = ({ list, loading }) => {
  return (
    <div className={styles['styledUserList']}>
      {/* {loading && <LoadingContainer />} */}
      {!loading &&
        list.map((item) => {
          const avatar = item.avatar || concatImageUrl(AvatarIconPNG);

          return (
            <div key={item.id} className={styles['styledUserItem']}>
              <Avatar alt={item.name} size={24} src={avatar} style={{ marginRight: '8px' }} />
              <div className={styles['styledUserInfo']}>
                <div className={styles['styledUserName']}>{item.name}</div>
                <div className={styles['styledUserEmail']}>{item.email}</div>
              </div>
            </div>
          );
        })}
    </div>
  );
};
