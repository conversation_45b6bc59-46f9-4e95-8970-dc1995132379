.styledUserList {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  flex: 1;
  height: 0;
}

.styledUserItem {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  cursor: pointer;
  margin-top: 4px;
  margin-bottom: 4px;
  height: 48px;
  box-sizing: border-box;

  &:hover {
    background-color: var(--theme-menu-color-bg-hover);
  }
}

.styledUserInfo {
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 0;
}

.styledUserName {
  color: var(--theme-text-color-default);
  font-size: 13px;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.styledUserEmail {
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
