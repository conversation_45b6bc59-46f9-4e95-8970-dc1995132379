import s18n from '@shimo/simple-i18n';
import React from 'react';

import type { ThirdUser } from '@/contexts/activeRange/type';

import { StepTwoHeader } from './components/StepTwoHeader';
import { StepTwoUserList } from './components/StepTwoUserList/LeftPart';
import styles from './index.less';

interface Props {
  disableUsers: ThirdUser[];
  loading: boolean;
}

export const RightPart: React.FC<Props> = ({ disableUsers, loading }) => {
  return (
    <div className={styles['styleRight']}>
      <StepTwoHeader isDisabled={true} num={disableUsers.length} title={s18n('新禁用成员')} />
      <StepTwoUserList list={disableUsers} loading={loading} />
    </div>
  );
};
