import s18n from '@shimo/simple-i18n';
import React from 'react';

import type { ThirdUser } from '@/contexts/activeRange/type';

import { StepTwoHeader } from './components/StepTwoHeader';
import { StepTwoUserList } from './components/StepTwoUserList/LeftPart';
import styles from './index.less';

interface Props {
  activateUsers: ThirdUser[];
  loading: boolean;
}

export const LeftPart: React.FC<Props> = ({ activateUsers, loading }) => {
  return (
    <div className={styles['StyleLeft']}>
      <StepTwoHeader isDisabled={false} num={activateUsers.length} title={s18n('新激活成员')} />
      <StepTwoUserList list={activateUsers} loading={loading} />
    </div>
  );
};
