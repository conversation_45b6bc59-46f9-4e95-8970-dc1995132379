import s18n from '@shimo/simple-i18n';
import React from 'react';

import styles from './index.less';

interface Props {
  title: string;
  num: number;
  isDisabled: boolean;
}

export const StepTwoHeader: React.FC<Props> = ({ title, num }) => {
  return (
    <div className={styles['StyledHeader']}>
      <div className={styles['StyledTitle']}>{title}</div>
      <div className={styles['StyledNumInfo']}>
        <span className={styles['StyledNum']}>{num}</span>
        <span>{s18n('位')}</span>
      </div>
    </div>
  );
};
