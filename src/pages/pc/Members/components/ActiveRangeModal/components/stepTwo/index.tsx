import React, { useContext, useEffect, useState } from 'react';

import { ActiveRangeContext } from '@/contexts/activeRange';
import { getScopeDiff } from '@/contexts/activeRange/service/api';
import type { ThirdUser } from '@/contexts/activeRange/type';
import { useMeStore } from '@/store/Me';

import { LeftPart } from './LeftPart';
import { RightPart } from './RightPart';

interface Props {
  setActiveUserCount: React.Dispatch<React.SetStateAction<number>>;
}
export const StepTwo: React.FC<Props> = ({ setActiveUserCount }) => {
  const { selectedBlacklist, selectedDepartments, selectedUsers } = useContext(ActiveRangeContext);
  const me = useMeStore((state) => state.me);

  const [activateUsers, setActivateUsers] = useState<ThirdUser[]>([]);
  const [disableUsers, setDisableUsers] = useState<ThirdUser[]>([]);
  const [loading, setLoading] = useState(false);

  const getDiffInfo = async () => {
    setLoading(true);
    try {
      const {
        data: { activateUsers, disableUsers },
      } = await getScopeDiff(me!.teamId, {
        userBlacklist: selectedBlacklist.map((item) => item.id),
        departmentIDs: selectedDepartments.map((item) => item.id),
        userIDs: selectedUsers.map((item) => item.id),
      });
      setActivateUsers(activateUsers);
      setActiveUserCount(activateUsers.length || 0);
      setDisableUsers(disableUsers);
    } catch (error) {}
    setLoading(false);
  };

  useEffect(() => {
    if (me && me.teamId) {
      getDiffInfo();
    }
  }, [me]);

  return (
    <div style={{ display: 'flex' }}>
      <LeftPart activateUsers={activateUsers} loading={loading} />
      <RightPart disableUsers={disableUsers} loading={loading} />
    </div>
  );
};
