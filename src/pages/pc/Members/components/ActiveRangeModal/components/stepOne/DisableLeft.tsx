import s18n from '@shimo/simple-i18n';
import { Input, Tooltip } from 'antd';
import classNames from 'classnames';
import React, { useContext, useEffect, useState } from 'react';

import EmptyPNG from '@/assets/images/empty/<EMAIL>';
import AvatarIconSVG from '@/assets/images/members/avatar-icon.png';
import { ReactComponent as CloseIconSVG } from '@/assets/images/members/close-icon.svg';
import { ReactComponent as NoticeIconSVG } from '@/assets/images/members/notice-icon.svg';
import { ActiveRangeContext, ActiveRangeDispatchContext } from '@/contexts/activeRange';
import { searchUserAndDepartment } from '@/contexts/activeRange/service/api';
import type { ThirdUser } from '@/contexts/activeRange/type';
import { useMeStore } from '@/store/Me';
import { concatImageUrl } from '@/utils/image';

import { SearchEmpty } from '../SearchEmpty';
import styles from './DisableLeft.less';
import { StyledDisableNum, StyleLeft } from './DisableLeft.style';

const { Search } = Input;

interface Props {
  changeDisableMode: (v: boolean) => void;
  inDisableMode: boolean;
}

export const DisableLeft: React.FC<Props> = ({ changeDisableMode, inDisableMode }) => {
  const { dispatch, saveCountSeat } = useContext(ActiveRangeDispatchContext);
  const { selectedBlacklist, searchUserList, selectedDepartments, selectedUsers } = useContext(ActiveRangeContext);
  const [inSearchMode, setInSearchMode] = useState(false);
  const me = useMeStore((state) => state.me);

  const [searchLoading, setSearchLoading] = useState(false);

  // 搜索用户和部门
  const handleSearch = async (keyword: string) => {
    if (!keyword) {
      // 清空搜索缓存
      dispatch?.({
        type: 'setSearchUserList',
        payload: {
          searchUserList: [],
        },
      });
      setInSearchMode(false);
      return;
    }

    setSearchLoading(true);
    setInSearchMode(true);

    try {
      const {
        data: { users },
      } = await searchUserAndDepartment(me!.teamId, keyword);
      dispatch?.({
        type: 'setSearchUserList',
        payload: {
          searchUserList: users,
        },
      });
    } catch (error) {}
    setSearchLoading(false);
  };

  const handleUserClick = (user: ThirdUser) => {
    setInSearchMode(false);

    // 1. 将用户放进黑名单中 (零时保存)
    if (selectedBlacklist.findIndex((item) => item.id === user.id) === -1) {
      const tempList = [...selectedBlacklist, user];
      dispatch?.({
        type: 'setSelectedBlacklist',
        payload: {
          selectedBlacklist: tempList,
        },
      });
      // 2. 保存禁用人数
      // 发送查询请求更改席位数量
      saveCountSeat?.(me!.teamId, {
        departments: selectedDepartments.map((item) => item.id),
        users: selectedUsers.map((item) => item.id),
        userBlacklist: tempList.map((item) => item.id),
      });
    }
  };

  const handleUserCancel = (user: ThirdUser) => {
    // 1. 将用户从黑名单中移除 (零时保存)
    const tempList = selectedBlacklist.filter((item) => item.id !== user.id);
    dispatch?.({
      type: 'setSelectedBlacklist',
      payload: {
        selectedBlacklist: tempList,
      },
    });
    // 2. 保存禁用人数
    // 发送查询请求更改席位数量
    saveCountSeat?.(me!.teamId, {
      departments: selectedDepartments.map((item) => item.id),
      users: selectedUsers.map((item) => item.id),
      userBlacklist: tempList.map((item) => item.id),
    });
  };

  useEffect(() => {
    return () => {
      // 清空用户搜索列表
      dispatch?.({
        type: 'setSearchUserList',
        payload: {
          searchUserList: [],
        },
      });
    };
  }, []);

  return (
    <StyleLeft
      className={classNames({
        active: inDisableMode,
      })}
    >
      <div className={styles['styledHeader']}>
        <div className={styles['styledTitle']}>
          <span key={1}>{s18n.x`禁用指定成员 ${(
            <StyledDisableNum
              key={2}
              className={classNames({
                highlight: selectedBlacklist.length > 0,
              })}
            >
              {selectedBlacklist.length}
            </StyledDisableNum>
          )} 人`}</span>
          <Tooltip
            title={s18n(
              '「禁用指定成员」中选择的企业成员，其石墨账号始终都不会被激活。若想重新激活该账号，请「解除禁用」。',
            )}
          >
            <span className={styles['styledNoticeContainer']}>
              <NoticeIconSVG />
            </span>
          </Tooltip>
        </div>
        <div
          className={styles['styledClose']}
          onClick={() => {
            changeDisableMode(false);
          }}
        >
          <CloseIconSVG />
        </div>
      </div>
      <div className={styles['styledInputContainer']}>
        <Search allowClear placeholder={s18n('搜索并添加禁用成员')} onSearch={handleSearch} />
        {inSearchMode && (
          <div className={styles['styledSearchList']}>
            {!searchLoading && searchUserList.length === 0 && <SearchEmpty />}
            {/* {searchLoading && <LoadingContainer />} */}
            {!searchLoading &&
              searchUserList.length > 0 &&
              searchUserList.map((item) => {
                const avatar = item.avatar || concatImageUrl(AvatarIconSVG);

                return (
                  <div
                    key={item.id}
                    className={styles['styledSearchUser']}
                    onClick={() => {
                      handleUserClick(item);
                    }}
                  >
                    <img alt={item.name} className={styles['styledSearchUserAvatar']} src={avatar} />
                    <div>
                      <div className={styles['styledUserName']}>{item.name}</div>
                      <div className={styles['styledUserEmail']}>{item.email}</div>
                    </div>
                  </div>
                );
              })}
          </div>
        )}
      </div>
      <div className={styles['styledListContainer']}>
        {selectedBlacklist.length > 0 && (
          <div className={styles['styledListHeader']}>{s18n('以下成员将始终不会同步至激活范围')}</div>
        )}
        <div className={styles['styledList']}>
          {selectedBlacklist.length === 0 ? (
            <div className={styles['styledEmpty']}>
              <img className={styles['styledEmptyIcon']} src={concatImageUrl(EmptyPNG)} />
              <div>{s18n('未禁用任何成员')}</div>
            </div>
          ) : (
            selectedBlacklist.map((item) => {
              const avatar = item.avatar || concatImageUrl(AvatarIconSVG);

              return (
                <div key={item.id} className={styles['styledListItem']}>
                  <div className={styles['styledListItemLeft']}>
                    <img alt={item.name} className={styles['styledListItemAvatar']} src={avatar} />
                    <div>
                      <div className={styles['styledListItemName']}>{item.name}</div>
                      <div className={styles['styledListItemEmail']}>{item.email}</div>
                    </div>
                  </div>
                  <div
                    className={styles['styledClose']}
                    onClick={() => {
                      handleUserCancel(item);
                    }}
                  >
                    <CloseIconSVG />
                  </div>
                </div>
              );
            })
          )}
        </div>
      </div>
    </StyleLeft>
  );
};
