import s18n from '@shimo/simple-i18n';
import { Input, Pagination } from 'antd';
import type { CheckboxChangeEvent } from 'antd/lib/checkbox';
import classNames from 'classnames';
import React, { useContext, useEffect, useState } from 'react';

import EmptyFillPNG from '@/assets/images/empty/<EMAIL>';
import { ReactComponent as DownIconSVG } from '@/assets/images/members/down-icon.svg';
import { ActiveRangeContext, ActiveRangeDispatchContext } from '@/contexts/activeRange';
import {
  getDepartmentUserList,
  getSubDepartment,
  getThirdCountSeat,
  getThirdRootDepartment,
  getThirdSelectedScope,
  ROOT_ID,
  searchUserAndDepartment,
} from '@/contexts/activeRange/service/api';
import type { ThirdDepartment, ThirdUser } from '@/contexts/activeRange/type';
import { useMeStore } from '@/store/Me';
import { concatImageUrl } from '@/utils/image';

import { DepartmentItem } from '../DepartmentItem';
import { LeftCheckAll } from '../LeftCheckAll';
import { LeftDisableSetting } from '../LeftDisableSetting';
import { UnselectedWarning } from '../UnselectedWarning';
import { UserItem } from '../UserItem';
import styles from './LeftPart.less';
import { StyleLeft } from './LeftPart.style';

const { Search } = Input;
const PageSize = 100;
const cacheDepartmentList = new Map<
  string,
  {
    id: string;
    parentId: string | null;
    name: string;
  }
>();

interface Props {
  changeDisableMode: (v: boolean) => void;
  inDisableMode: boolean;
}

export const LeftPart: React.FC<Props> = ({ changeDisableMode, inDisableMode }) => {
  cacheDepartmentList.set('0', {
    id: '0',
    name: s18n('通讯录'),
    parentId: null,
  });
  const [departmentList, setDepartmentList] = useState<ThirdDepartment[]>([]);
  const [userList, setUserList] = useState<ThirdUser[]>([]);
  const [userTotal, setUserTotal] = useState(0);
  const [currentDepartmentId, setCurrentDepartmentId] = useState('0');
  const [loading, setLoading] = useState(false);
  const [inSearchMode, setInSearchMode] = useState(false);

  const me = useMeStore((state) => state.me);

  const [breadList, setBreadList] = useState<{ name: string; id: string; parentId: string | null }[]>([]);
  const { dispatch, saveCountSeat } = useContext(ActiveRangeDispatchContext);
  const { selectedBlacklist, selectedDepartments, selectedUsers, searchDepartmentList, searchUserList } =
    useContext(ActiveRangeContext);

  /**
   * 获取根部门信息
   */
  const getRootDepartmentInfo = async () => {
    try {
      const { data } = await getThirdRootDepartment({ teamId: me!.teamId });
      cacheDepartmentList.set('1', {
        ...data,
        parentId: ROOT_ID,
      });
      setCurrentDepartmentId(ROOT_ID);
      setDepartmentList([data]);
    } catch (error) {}
  };

  /**
   * 获取部门列表
   */
  const getDepartmentList = async (departmentId: string) => {
    try {
      const {
        data: { departments },
      } = await getSubDepartment({
        departmentId,
        teamId: me!.teamId,
      });
      setDepartmentList(departments);
      departments.forEach((item) => {
        cacheDepartmentList.set(item.id, {
          ...item,
          parentId: departmentId,
        });
      });

      setCurrentDepartmentId(departmentId);
    } catch (error) {
      console.error(error);
    }
  };

  /**
   * 获取用户列表
   */
  const getUserList = async ({ page, departmentId }: { page: number; departmentId: string }) => {
    try {
      const {
        data: { totalCount, users },
      } = await getDepartmentUserList({
        page,
        departmentId,
        teamId: me!.teamId,
      });
      setUserTotal(totalCount);
      setUserList(users);
    } catch (error) {
      console.error('error ->', error);
    }
  };

  /**
   * 处理部门选中
   */
  const handleCheckDepartment = (e: CheckboxChangeEvent, department: ThirdDepartment) => {
    let list = [...selectedDepartments];
    if (e.target.checked) {
      if (!selectedDepartments.find((item) => item.id === department.id)) {
        list = list.concat([department]);
      }
    } else {
      list = list.filter((item) => item.id !== department.id);
    }

    dispatch?.({
      type: 'setSelectedDepartments',
      payload: {
        selectedDepartments: list,
      },
    });

    // 发送请求更改席位数量
    saveCountSeat?.(me!.teamId, {
      departments: list.map((item) => item.id),
      users: selectedUsers.map((item) => item.id),
      userBlacklist: selectedBlacklist.map((item) => item.id),
    });
  };

  /**
   * 处理部门链接按钮
   */
  const handleDepartmentLinkButton = async (department: ThirdDepartment) => {
    setLoading(true);
    try {
      await getDepartmentList(department.id);
      await getUserList({
        page: 1,
        departmentId: department.id,
      });
    } catch (error) {}
    setLoading(false);
  };

  const handleCheckUser = (e: CheckboxChangeEvent, user: ThirdUser) => {
    let list = [...selectedUsers];
    if (e.target.checked) {
      if (!selectedUsers.find((item) => item.id === user.id)) {
        list = list.concat([user]);
      }
    } else {
      list = list.filter((item) => item.id !== user.id);
    }
    dispatch?.({
      type: 'setSelectedUsers',
      payload: {
        selectedUsers: list,
      },
    });

    // 发送请求更改席位数量
    saveCountSeat?.(me!.teamId, {
      departments: selectedDepartments.map((item) => item.id),
      users: list.map((item) => item.id),
      userBlacklist: selectedBlacklist.map((item) => item.id),
    });
  };

  /**
   * 处理面包屑点击
   */
  const handleBreadClick = async (departmentId: string) => {
    setLoading(true);
    if (departmentId === ROOT_ID) {
      await getRootDepartmentInfo();
      await getUserList({
        page: 1,
        departmentId: ROOT_ID,
      });
    } else {
      await getDepartmentList(departmentId);
      await getUserList({
        page: 1,
        departmentId,
      });
    }
    setLoading(false);
  };

  const getScopeData = async () => {
    try {
      const {
        data: { blacklistUsers, departments, users },
      } = await getThirdSelectedScope(me!.teamId);
      dispatch?.({
        type: 'setSelectedBlacklist',
        payload: {
          selectedBlacklist: blacklistUsers,
        },
      });
      dispatch?.({
        type: 'setSelectedDepartments',
        payload: {
          selectedDepartments: departments,
        },
      });
      dispatch?.({
        type: 'setSelectedUsers',
        payload: {
          selectedUsers: users,
        },
      });

      const {
        data: { seatCount },
      } = await getThirdCountSeat(me!.teamId, {
        departments: departments.map((item) => item.id),
        users: users.map((item) => item.id),
        userBlacklist: blacklistUsers.map((item) => item.id),
      });

      dispatch?.({
        type: 'setSelectedSeatCount',
        payload: {
          selectedSeatCount: seatCount,
        },
      });
    } catch (error) {}
  };

  // 搜索用户和部门
  const handleSearch = async (keyword: string) => {
    if (!keyword) {
      setInSearchMode(false);
      dispatch?.({
        type: 'setSearchDepartmentList',
        payload: {
          searchDepartmentList: [],
        },
      });
      dispatch?.({
        type: 'setSearchUserList',
        payload: {
          searchUserList: [],
        },
      });
      return;
    }
    setInSearchMode(true);
    setLoading(true);
    try {
      const {
        data: { departments, users },
      } = await searchUserAndDepartment(me!.teamId, keyword);
      dispatch?.({
        type: 'setSearchDepartmentList',
        payload: {
          searchDepartmentList: departments,
        },
      });
      dispatch?.({
        type: 'setSearchUserList',
        payload: {
          searchUserList: users,
        },
      });
    } catch (error) {}
    setLoading(false);
  };

  /**
   * 初始化
   */
  const init = async () => {
    setLoading(true);
    try {
      await getRootDepartmentInfo();
      await getUserList({
        page: 1,
        departmentId: ROOT_ID,
      });
      await getScopeData();
    } catch (error) {}
    setLoading(false);
  };

  useEffect(() => {
    if (me?.id) {
      init();
    }
  }, [me]);

  useEffect(() => {
    const breadList: { parentId: string | null; name: string; id: string }[] = [];
    let parentId = currentDepartmentId;

    while (cacheDepartmentList.get(parentId)) {
      const department = cacheDepartmentList.get(parentId)!;
      breadList.unshift(department);
      parentId = department.parentId!;
    }
    setBreadList(breadList);
  }, [currentDepartmentId]);

  const displayDepartments = inSearchMode ? searchDepartmentList : departmentList;

  const displayUsers = inSearchMode ? searchUserList : userList;

  return (
    <StyleLeft
      className={classNames({
        active: !inDisableMode,
      })}
    >
      <div className={styles['styledTitle']}>{s18n('企业组织架构')}</div>
      <div className={styles['styledInputContainer']}>
        <Search allowClear placeholder={s18n('搜索部门或企业成员')} onSearch={handleSearch} />
      </div>
      <UnselectedWarning />
      {!inSearchMode && (
        <div className={styles['styledBreadcrumb']}>
          {breadList.map((item, index) => {
            return (
              <React.Fragment key={item.id}>
                <div
                  key={item.id}
                  className={styles['styledNormalBread']}
                  onClick={() => {
                    handleBreadClick(item.id);
                  }}
                >
                  {item.name}
                </div>
                {index !== breadList.length - 1 && <DownIconSVG className={styles['styledRightIcon']} />}
              </React.Fragment>
            );
          })}
        </div>
      )}
      <div className={styles['styledListContainer']}>
        <LeftCheckAll departmentList={departmentList} userList={userList} />
        <div className={styles['styledList']}>
          {/* {loading ? <LoadingContainer /> : null} */}
          {!loading &&
            displayDepartments.map((item) => {
              const disabled = selectedDepartments.find((department) => department.id === item.id) !== undefined;

              return (
                <DepartmentItem
                  key={item.id}
                  checked={
                    !!selectedDepartments.find((department) => {
                      return department.id === item.id;
                    })
                  }
                  department={item}
                  disabled={disabled}
                  handleCheckDepartment={handleCheckDepartment}
                  handleDepartmentLinkButton={handleDepartmentLinkButton}
                />
              );
            })}
          {!loading &&
            displayUsers.map((item) => {
              const disabled = selectedBlacklist.find((user) => user.id === item.id) !== undefined;
              return (
                <UserItem
                  key={item.id}
                  checked={
                    !!selectedUsers.find((user) => {
                      return user.id === item.id;
                    })
                  }
                  disabled={disabled}
                  handleCheckUser={handleCheckUser}
                  user={item}
                />
              );
            })}
          {!loading && inSearchMode && displayUsers.length === 0 && (
            <div className={styles['styledEmptyContainer']}>
              <img className={styles['styledEmptyIMG']} src={concatImageUrl(EmptyFillPNG)} />
              <div className={styles['styledEmptyText']}>{s18n('暂无数据')}</div>
            </div>
          )}
          {!inSearchMode && userTotal > PageSize ? (
            <Pagination
              pageSize={PageSize}
              showSizeChanger={false}
              size="small"
              total={userTotal}
              onChange={(page) => {
                getUserList({ page, departmentId: currentDepartmentId });
              }}
            />
          ) : null}
        </div>
        <LeftDisableSetting changeDisableMode={changeDisableMode} />
      </div>
    </StyleLeft>
  );
};
