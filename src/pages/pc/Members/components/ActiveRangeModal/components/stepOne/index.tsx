import React, { useState } from 'react';

import { DisableLeft } from './DisableLeft';
import { LeftPart } from './LeftPart';
import { RightPart } from './RightPart';

export const StepOne: React.FC = () => {
  const [inDisableMode, setInDisableMode] = useState(false);

  const changeDisableMode = (v: boolean) => {
    setInDisableMode(v);
  };

  return (
    <div style={{ display: 'flex' }}>
      <DisableLeft changeDisableMode={changeDisableMode} inDisableMode={inDisableMode} />
      <LeftPart changeDisableMode={changeDisableMode} inDisableMode={inDisableMode} />
      <RightPart />
    </div>
  );
};
