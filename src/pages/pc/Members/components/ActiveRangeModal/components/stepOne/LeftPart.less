.styledTitle {
  padding: 8px 12px;
  color: var(--theme-text-color-default);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
}

.styleLeft {
  flex-basis: 50%;
  height: 522px;
  border: 1px solid var(--theme-separator-color-lighter);
  flex-direction: column;
  width: 50%;
  display: none;

  &.active {
    display: flex;
  }
}

.styledEmptyContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

// = styled.img`
.styledEmptyIMG {
  width: 160px;
  height: 130px;
  border-radius: 50%;
}

.styledEmptyText {
  color: var(--theme-text-color-secondary);
}

.styledInputContainer {
  padding: 0 12px;
}

.styledBreadcrumb {
  padding: 8px;
  margin-left: 0;
  margin-right: 12px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid var(--theme-separator-color-lighter);
  overflow-x: auto;
}

.styledNormalBread {
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  line-height: 20px;
  cursor: pointer;
  word-break: keep-all;
  margin-right: 4px;
  margin-left: 4px;

  &:last-child {
    color: var(--theme-text-color-deep);
    font-weight: 500;
  }
}

.styledListContainer {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 0;
}

.styledCheckAll {
  color: var(--theme-text-color-default);
  font-size: 13px;
  line-height: 20px;
  padding: 8px 12px;
}

.styledCheckAllText {
  color: var(--theme-text-color-default);
  font-size: 13px;
  line-height: 20px;
}

.styledList {
  flex: 1;
  height: 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.styledDisableSetting {
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid var(--theme-separator-color-lighter);
  padding: 0 12px;
}

.styledDisableLeft {
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  line-height: 20px;
  display: flex;
  align-items: center;
}

.styledRightLink {
  font-size: 13px;
  line-height: 20px;
  color: var(--theme-link-button-color);
  cursor: pointer;

  &:hover {
    color: var(--theme-link-button-color-hover);
  }
}

// = styled(DownIconSVG)`
.styledRightIcon {
  transform: rotate(-90deg);
  flex-shrink: 0;
}

// = styled(Pagination)`
.styledPagination {
  font-size: 12px;
}

// = styled.span`
.styledDisableNum {
  margin-left: 4px;
  margin-right: 4px;

  &.highlight {
    color: var(--theme-text-color-alert);
  }
}

// = styled.span`
.styledNoticeContainer {
  color: var(--theme-text-color-disabled);
  margin-left: 4px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
