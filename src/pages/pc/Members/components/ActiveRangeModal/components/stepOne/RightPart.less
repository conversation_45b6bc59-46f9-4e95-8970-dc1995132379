.styledTitle {
  color: var(--theme-text-color-default);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  display: flex;
  flex-direction: column;
}

.styledRight {
  flex-basis: 50%;
  height: 522px;
  border: 1px solid var(--theme-separator-color-lighter);
  border-left: transparent;
  display: flex;
  flex-direction: column;
}

.styledRightHeader {
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid var(--theme-separator-color-lighter);
  padding: 8px 12px;

  & > div {
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.styledSelectionInfo {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.styledSelectionLeft {
  font-size: 12px;
}

.styledExceedNotice {
  display: flex;
  align-items: center;
  font-size: 13px;
  line-height: 20px;
  color: var(--theme-text-color-alert);

  & > span {
    margin-left: 4px;
  }
}
// = styled.span`
.styledSelectedSeatCount {
  color: var(--theme-text-color-default);

  &.hasExceed {
    color: var(--theme-text-color-alert);
  }
}

.styledSelectionRight {
  color: var(--theme-link-button-color);
  font-size: 14px;
  line-height: 22px;
  cursor: pointer;

  &:hover {
    color: var(--theme-link-button-color-hover);
  }
}

.styledSelectionList {
  flex: 1;
  height: 0;
  display: flex;
  flex-direction: column;
  padding: 4px 0;
  overflow-y: auto;

  & > div {
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.styleItemLeft {
  display: flex;
  height: 100%;
  align-items: center;

  & > div {
    margin-left: 8px;
  }
}

.styledItemTitle {
  color: var(--theme-text-color-default);
  font-size: 13px;
  line-height: 20px;
}

.styledDepartmentItem {
  height: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 12px;
  padding-right: 12px;
  cursor: pointer;
  flex-shrink: 0;

  &:hover {
    background: var(--theme-menu-color-bg-hover);
  }
}

.styledUserItem {
  // height: '${({ $height }) => $height}px';
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 12px;
  padding-right: 12px;
  flex-shrink: 0;
  cursor: pointer;

  &:hover:not(.disabled) {
    background: var(--theme-menu-color-bg-hover);
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.3;
  }
}

.styledUserName {
  color: var(--theme-text-color-default);
  font-size: 13px;
  line-height: 20px;
}

.styledUserEmail {
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  line-height: 20px;
}

.styledCloseContainer {
  display: flex;
  color: var(--theme-text-color-secondary);
}

.styledCloseIcon {
  cursor: pointer;
}

.styledEmptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 94px;
}

.styledEmptySelected {
  width: 160px;
  height: 130px;
}

.styledEmptyText {
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  line-height: 20px;
}
