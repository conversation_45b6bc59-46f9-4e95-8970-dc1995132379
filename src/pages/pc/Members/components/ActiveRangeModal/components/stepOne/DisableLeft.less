.styledHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
}

.styledTitle {
  color: var(--theme-text-color-default);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  display: flex;
  align-items: center;
}

.styledClose {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.styleLeft {
  flex-basis: 50%;
  height: 522px;
  border: 1px solid var(--theme-separator-color-lighter);
  display: flex;
  flex-direction: column;
  width: 50%;

  &.active {
    display: flex;
  }
}

.styledInputContainer {
  margin: 0 12px;
  position: relative;
}

.styledListContainer {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 0;
}

.styledSearchList {
  top: calc(100% + 4px);
  background: var(--theme-layout-color-bg-white);
  max-height: 264px;
  overflow-y: auto;
  position: absolute;
  width: 100%;
  border-radius: 2px;
  display: flex;
  padding: 4px 0;
  flex-direction: column;
  box-shadow:
    0 3px 6px -4px rgba(0, 0, 0, 12%),
    0 6px 16px 0 rgba(0, 0, 0, 8%),
    0 9px 28px 8px rgba(0, 0, 0, 5%);
}

.styledSearchUser {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  cursor: pointer;

  &:hover {
    background-color: var(--theme-menu-color-bg-hover);
  }
}

// = styled.img`
.styledSearchUserAvatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
}

.styledUserName {
  color: var(--theme-text-color-default);
  font-size: 14px;
  line-height: 22px;
}

.styledUserEmail {
  color: var(--theme-text-color-secondary);
  font-size: 14px;
  line-height: 22px;
}

.styledEmpty {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  flex: 1;
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  font-weight: 400;
  line-height: 20px;
}

// = styled.img`
.styledEmptyIcon {
  width: 160px;
  height: 130px;
}

.styledListHeader {
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  line-height: 20px;
  padding: 8px 12px;
}

.styledList {
  flex: 1;
  height: 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.styledListItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 44px;
  box-sizing: border-box;
  padding: 4px 12px;
  cursor: pointer;

  &:hover {
    background-color: var(--theme-menu-color-bg-hover);
  }
}

.styledListItemLeft {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

// = styled.img`
.styledListItemAvatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
}

.styledListItemName {
  color: var(--theme-text-color-default);
  font-size: 13px;
  line-height: 20px;
}

.styledListItemEmail {
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  line-height: 20px;
}

// = styled.span`
.styledDisableNum {
  &.highlight {
    color: var(--theme-text-color-alert);
  }
}

// = styled.span`
.styledNoticeContainer {
  color: var(--theme-text-color-disabled);
  display: flex;
  align-items: center;
  margin-left: 4px;
  cursor: pointer;
}
