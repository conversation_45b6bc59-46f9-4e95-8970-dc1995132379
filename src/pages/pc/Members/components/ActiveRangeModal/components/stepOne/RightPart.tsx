import s18n from '@shimo/simple-i18n';
import { Avatar, Tooltip } from 'antd';
import classNames from 'classnames';
import React, { useContext, useState } from 'react';

import EmptyPNG from '@/assets/images/empty/<EMAIL>';
import AvatarIconPNG from '@/assets/images/members/avatar-icon.png';
import { ReactComponent as CloseIconSVG } from '@/assets/images/members/close-icon.svg';
import { ReactComponent as DepartmentIconSVG } from '@/assets/images/members/department-icon.svg';
import { ReactComponent as NoticeIconSvg } from '@/assets/images/members/notice-icon.svg';
import { ActiveRangeContext, ActiveRangeDispatchContext } from '@/contexts/activeRange';
import type { ThirdDepartment, ThirdUser } from '@/contexts/activeRange/type';
import { PermissionContext } from '@/contexts/permissions';
import { useMeStore } from '@/store/Me';
import { concatImageUrl } from '@/utils/image';

import { DingdingTipModal } from '../../../DingdingTipModal';
import styles from './RightPart.less';
import { StyledSelectedSeatCount, StyledUserItem } from './RightPart.style';

export const RightPart: React.FC = () => {
  const { teamInfo } = useContext(PermissionContext);
  const me = useMeStore((state) => state.me);

  const total = teamInfo?.membership?.memberCount;
  const { selectedDepartments, selectedUsers, selectedSeatCount, selectedBlacklist, getThirdCountSeatLoading } =
    useContext(ActiveRangeContext);
  const { dispatch, saveCountSeat } = useContext(ActiveRangeDispatchContext);
  const [dingdingTipOpen, setDingdingTipOpen] = useState(false);

  const cancelDepartment = (department: ThirdDepartment) => {
    const list = selectedDepartments.filter((item) => item.id !== department.id);
    dispatch?.({
      type: 'setSelectedDepartments',
      payload: {
        selectedDepartments: list,
      },
    });
    saveCountSeat?.(me!.teamId, {
      departments: list.map((item) => item.id!),
      users: selectedUsers.map((item) => item.id!),
      userBlacklist: selectedBlacklist.map((item) => item.id!),
    });
  };

  const cancelUser = (user: ThirdUser) => {
    const list = selectedUsers.filter((item) => item.id !== user.id);
    dispatch?.({
      type: 'setSelectedUsers',
      payload: {
        selectedUsers: list,
      },
    });
    saveCountSeat?.(me!.teamId, {
      departments: selectedDepartments.map((item) => item.id!),
      users: list.map((item) => item.id!),
      userBlacklist: selectedBlacklist.map((item) => item.id!),
    });
  };

  const hasExceed = total && selectedSeatCount > 0 && selectedSeatCount > total;

  const handleDingdingTip = () => {
    // 目前老代码 src/enterprise/pages/billing/TeamStatus.tsx
    // 其中判断钉钉的方式是 通过 /orders/action/can_order 返回的结果得出的
    // const isDingtalkPaidEnterprise = !billing.canOrder;
    // 但是这个页面并没有发送这个接口请求 使用的默认 false
    // 也就是说 这个订购按钮 目前只会弹出钉钉的提示
    setDingdingTipOpen(true);
  };

  const isEmpty = selectedUsers.length === 0 && selectedDepartments.length === 0;

  return (
    <>
      <div className={styles['styledRight']}>
        <div className={styles['styledRightHeader']}>
          <div className={styles['styledTitle']}>{s18n('激活范围')}</div>
          <div className={styles['styledSelectionInfo']}>
            {getThirdCountSeatLoading ? (
              // <Loading hasMarginBottom={false} />
              <span>loading</span>
            ) : (
              <div style={{ fontSize: '12px' }}>
                {s18n('已选择')}
                <span> </span>
                <StyledSelectedSeatCount
                  className={classNames({
                    hasExceed,
                  })}
                >
                  {selectedSeatCount}
                </StyledSelectedSeatCount>
                {` / ${total || 0}`}
              </div>
            )}
            <div className={styles['styledSelectionRight']} onClick={handleDingdingTip}>
              {s18n('增购席位')}
            </div>
          </div>
          {hasExceed && (
            <div className={styles['styledExceedNotice']}>
              <NoticeIconSvg />
              <span>{s18n`已超出席位数量上限，请减少激活人数或增购席位`}</span>
            </div>
          )}
        </div>
        <div className={styles['styledSelectionList']}>
          {isEmpty && (
            <div className={styles['styledEmptyContainer']}>
              <img className={styles['styledEmptySelected']} src={concatImageUrl(EmptyPNG)} />
              <div className={styles['styledEmptyText']}>{s18n('请从左侧勾选成员以同步')}</div>
            </div>
          )}
          {selectedDepartments.map((item) => {
            return (
              <div key={item.id} className={styles['styledDepartmentItem']}>
                <div className={styles['styleItemLeft']}>
                  <DepartmentIconSVG />
                  <div className={styles['styledItemTitle']}>{item.name}</div>
                </div>
                <div
                  style={{ display: 'flex' }}
                  onClick={() => {
                    cancelDepartment(item);
                  }}
                >
                  <CloseIconSVG className={styles['styledCloseIcon']} />
                </div>
              </div>
            );
          })}
          {selectedUsers.map((item) => {
            const disabled = selectedBlacklist.findIndex((blackItem) => blackItem.id === item.id) !== -1;
            const avatar = item.avatar || concatImageUrl(AvatarIconPNG);
            return (
              <StyledUserItem
                // className={styles['StyledUserItem']}
                // style={{ height: item.email ? 48 : 32 }}
                key={item.id}
                $height={item.email ? 48 : 32}
                className={classNames({
                  disabled,
                })}
              >
                <Tooltip placement="topLeft" title={disabled ? s18n('该成员已被禁用') : ''}>
                  <div className={styles['styleItemLeft']}>
                    <Avatar size={24} src={avatar} />
                    <div>
                      <div className={styles['styledUserName']}>{item.name}</div>
                      <div className={styles['styledUserEmail']}>{item.email}</div>
                    </div>
                  </div>
                </Tooltip>
                <div
                  className={styles['styledCloseContainer']}
                  style={{ display: 'flex' }}
                  onClick={() => {
                    cancelUser(item);
                  }}
                >
                  <div className={styles['styledCloseIcon']} />
                </div>
              </StyledUserItem>
            );
          })}
        </div>
      </div>
      {dingdingTipOpen && (
        <DingdingTipModal
          changeOpen={(v: boolean) => {
            setDingdingTipOpen(v);
          }}
          open={dingdingTipOpen}
        />
      )}
    </>
  );
};
