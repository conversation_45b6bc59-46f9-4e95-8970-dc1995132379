import styled from 'styled-components';

export const StyledSelectedSeatCount = styled.span`
  color: var(--theme-text-color-default);

  &.hasExceed {
    color: var(--theme-text-color-alert);
  }
`;
export const StyledUserItem = styled.div<{ $height: number }>`
  height: '${({ $height }) => $height}px';
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 12px;
  padding-right: 12px;
  flex-shrink: 0;
  cursor: pointer;

  &:hover:not(.disabled) {
    background: var(--theme-menu-color-bg-hover);
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.3;
  }
`;
