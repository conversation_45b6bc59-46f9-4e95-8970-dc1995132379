import s18n from '@shimo/simple-i18n';
import { Checkbox } from 'antd';
import type { CheckboxChangeEvent } from 'antd/lib/checkbox';
import React, { useContext } from 'react';

import { ActiveRangeContext, ActiveRangeDispatchContext } from '@/contexts/activeRange';
import type { ThirdDepartment, ThirdUser } from '@/contexts/activeRange/type';
import { useMeStore } from '@/store/Me';

import styles from './index.less';

interface Props {
  departmentList: ThirdDepartment[];
  userList: ThirdUser[];
}

export const LeftCheckAll: React.FC<Props> = ({ departmentList, userList }) => {
  const { selectedDepartments, selectedUsers, selectedBlacklist } = useContext(ActiveRangeContext);
  const { dispatch, saveCountSeat } = useContext(ActiveRangeDispatchContext);
  const me = useMeStore((state) => state.me);

  const handleCheckAll = (e: CheckboxChangeEvent) => {
    let tmpDepartments: ThirdDepartment[] = [];
    let tmpUsers: ThirdUser[] = [];

    if (e.target.checked) {
      // 当前数据全选
      departmentList.forEach((item) => {
        if (!selectedDepartments.find((i) => i.id === item.id)) {
          tmpDepartments.push(item);
        }
      });
      tmpDepartments = [...selectedDepartments, ...tmpDepartments];
      // 当前用户全选
      userList.forEach((item) => {
        if (!selectedUsers.find((i) => i.id === item.id)) {
          tmpUsers.push(item);
        }
      });
      tmpUsers = [...selectedUsers, ...tmpUsers];
    } else {
      // 当前数据全不选
      // 从缓存中删除当前加载的数据
      tmpDepartments = selectedDepartments.filter((item) => {
        return !departmentList.find((i) => i.id === item.id);
      });
      tmpUsers = selectedUsers.filter((item) => {
        return !userList.find((i) => i.id === item.id);
      });
    }
    dispatch?.({
      type: 'setSelectedDepartments',
      payload: {
        selectedDepartments: tmpDepartments,
      },
    });
    dispatch?.({
      type: 'setSelectedUsers',
      payload: {
        selectedUsers: tmpUsers,
      },
    });
    // 发送请求更改席位数量
    saveCountSeat?.(me!.teamId, {
      departments: tmpDepartments.map((item) => item.id),
      users: tmpUsers.map((item) => item.id),
      userBlacklist: selectedBlacklist.map((item) => item.id),
    });
  };

  const checkAll = () => {
    let departmentFlag = true;
    departmentList.forEach((item) => {
      if (!selectedDepartments.find((i) => i.id === item.id)) {
        departmentFlag = false;
      }
    });

    let userFlag = true;
    userList.forEach((item) => {
      if (!selectedUsers.find((i) => i.id === item.id)) {
        userFlag = false;
      }
    });

    return departmentFlag && userFlag;
  };

  return (
    <div className={styles['styledCheckAll']}>
      <Checkbox
        checked={checkAll()}
        indeterminate={!checkAll && (selectedDepartments.length > 0 || selectedUsers.length > 0)}
        onChange={handleCheckAll}
      >
        <div className={styles['styledCheckAllText']}>{s18n('全选')}</div>
      </Checkbox>
    </div>
  );
};
