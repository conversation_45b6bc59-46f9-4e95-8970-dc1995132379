import s18n from '@shimo/simple-i18n';
import { Avatar } from 'antd';
import { useContext } from 'react';

import AvatarIconPNG from '@/assets/images/members/avatar-icon.png';
import { ActiveRangeContext, ActiveRangeDispatchContext } from '@/contexts/activeRange';
import type { ThirdUser } from '@/contexts/activeRange/type';
import { useMeStore } from '@/store/Me';
import { concatImageUrl } from '@/utils/image';

import styles from './index.less';
import { StyledItem } from './index.style';

export const UnselectedWarning = () => {
  const me = useMeStore((state) => state.me);

  const { unselectedCreator, unselectedCurrentUser, selectedBlacklist, selectedDepartments, selectedUsers } =
    useContext(ActiveRangeContext);
  const { dispatch, saveCountSeat } = useContext(ActiveRangeDispatchContext);
  if (!unselectedCreator && !unselectedCurrentUser) return null;

  const list = [unselectedCreator, unselectedCurrentUser].filter(Boolean);

  const handleUserClick = (user: ThirdUser) => {
    saveCountSeat?.(me!.teamId, {
      departments: selectedDepartments.map((item) => item.id),
      users: [...selectedUsers, user].map((item) => item.id),
      userBlacklist: selectedBlacklist.map((item) => item.id),
    }).then(() => {
      dispatch?.({
        type: 'setSelectedUsers',
        payload: {
          selectedUsers: [...selectedUsers, user],
        },
      });
    });
  };

  return (
    <div className={styles['styledUnselectedWarning']}>
      <div className={styles['styledWarningText']}>{s18n('激活范围中须包含自己和创建者或其所在部门')}</div>
      <div className={styles['styledWarningInfo']}>{s18n('点击下方头像添加角色到激活范围')}</div>
      <div className={styles['styledCreatorAndMe']}>
        {list.map((item) => {
          const avatar = item?.avatar || concatImageUrl(AvatarIconPNG);
          return (
            <StyledItem
              key={item?.id}
              $width={`${100 / list.length}%`}
              onClick={() => {
                handleUserClick(item!);
              }}
            >
              <Avatar size={24} src={avatar} />
              <div className={styles['styledItemName']}>{item?.name}</div>
            </StyledItem>
          );
        })}
      </div>
    </div>
  );
};
