.styledUnselectedWarning {
  display: flex;
  padding: 8px 12px;
  margin: 8px 12px 0;
  flex-direction: column;
  border-radius: 2px;
  background: var(--theme-layout-color-bg-editor);

  & > div {
    margin-bottom: 4px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.styledWarningText {
  color: var(--theme-text-color-alert);
  font-size: 13px;
  line-height: 20px;
}

.styledWarningInfo {
  color: var(--theme-text-color-medium);
  font-size: 13px;
  line-height: 20px;
}

.styledCreatorAndMe {
  display: flex;
  align-items: flex-start;
}

.styledItem {
  display: flex;
  align-items: flex-start;
  padding: 4px;
  width: '${({ $width }) => $width}';
  flex-shrink: 0;
  cursor: pointer;
  box-sizing: border-box;

  &:hover {
    background: var(--theme-menu-color-bg-hover);
  }

  &:active {
    background: var(--theme-menu-color-bg-active);
  }
}

.styledItemName {
  color: var(--theme-text-color-default);
  font-size: 13px;
  line-height: 24px;
  margin-left: 4px;
  flex: 1;
  width: 0;
}

.styledAvatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}
