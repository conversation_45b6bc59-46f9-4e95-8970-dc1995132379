import s18n from '@shimo/simple-i18n';
import { Tooltip } from 'antd';
import classNames from 'classnames';
import React, { useContext } from 'react';

import { ReactComponent as NoticeIconSVG } from '@/assets/images/members/notice-icon.svg';
import { ActiveRangeContext } from '@/contexts/activeRange';

import {
  StyledDisableLeft,
  StyledDisableNum,
  StyledDisableSetting,
  StyledNoticeContainer,
  StyledRightLink,
} from './index.style';

interface Props {
  changeDisableMode: (v: boolean) => void;
}

export const LeftDisableSetting: React.FC<Props> = ({ changeDisableMode }) => {
  const { selectedBlacklist } = useContext(ActiveRangeContext);

  return (
    <StyledDisableSetting>
      <StyledDisableLeft>
        {s18n.x`禁用指定成员 ${(
          <StyledDisableNum
            key={1}
            className={classNames({
              highlight: selectedBlacklist.length > 0,
            })}
          >
            {selectedBlacklist.length}
          </StyledDisableNum>
        )} 人`}
        <Tooltip
          title={s18n(
            '「禁用指定成员」中选择的企业成员，其石墨账号始终都不会被激活。若想重新激活该账号，请「解除禁用」。',
          )}
        >
          <StyledNoticeContainer>
            <NoticeIconSVG />
          </StyledNoticeContainer>
        </Tooltip>
      </StyledDisableLeft>

      <StyledNoticeContainer />
      <StyledRightLink
        onClick={() => {
          changeDisableMode(true);
        }}
      >
        {s18n('设置')}
      </StyledRightLink>
    </StyledDisableSetting>
  );
};
