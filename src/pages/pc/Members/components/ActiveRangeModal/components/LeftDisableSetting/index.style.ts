import styled from 'styled-components';

export const StyledDisableSetting = styled.div`
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top: 1px solid var(--theme-separator-color-lighter);
  padding: 0 12px;
`;

export const StyledDisableLeft = styled.div`
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  line-height: 20px;
  display: flex;
  align-items: center;
`;

export const StyledRightLink = styled.div`
  font-size: 13px;
  line-height: 20px;
  color: var(--theme-link-button-color);
  cursor: pointer;

  &:hover {
    color: var(--theme-link-button-color-hover);
  }
`;

export const StyledDisableNum = styled.span`
  margin-left: 4px;
  margin-right: 4px;

  &.highlight {
    color: var(--theme-text-color-alert);
  }
`;

export const StyledNoticeContainer = styled.span`
  color: var(--theme-text-color-disabled);
  margin-left: 4px;
  display: flex;
  align-items: center;
  cursor: pointer;
`;
