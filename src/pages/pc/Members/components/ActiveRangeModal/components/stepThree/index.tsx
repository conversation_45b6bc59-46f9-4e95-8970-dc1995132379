import s18n from '@shimo/simple-i18n';
import { Button } from 'antd';
import React, { useContext } from 'react';

import { ReactComponent as CheckCircleIconSVG } from '@/assets/images/members/check-circle-80.svg';
import { MembersDispatchContext } from '@/contexts/members';

import styles from './index.less';

interface Props {
  successNum: number;
  handleCancel: () => void;
  activeDepartmentId: number;
}

export const ActiveRangeSuccess: React.FC<Props> = ({ successNum = 0, handleCancel, activeDepartmentId }) => {
  const { refreshTableList } = useContext(MembersDispatchContext);

  return (
    <div className={styles['styleContent']}>
      <CheckCircleIconSVG />
      <div className={styles['StyledContentText']}>{s18n.x`已成功激活 ${(
        <span key={2} className={styles['StyledContentNum']}>
          {successNum}
        </span>
      )} 名成员至通讯录`}</div>
      <Button
        className={styles['styledButton']}
        type="primary"
        onClick={() => {
          refreshTableList?.(activeDepartmentId);
          handleCancel();
        }}
      >
        {s18n('完成')}
      </Button>
    </div>
  );
};
