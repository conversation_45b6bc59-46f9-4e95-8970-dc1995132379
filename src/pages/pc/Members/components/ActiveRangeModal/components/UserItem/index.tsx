import s18n from '@shimo/simple-i18n';
import { Avatar, Checkbox, Tooltip } from 'antd';
import type { CheckboxChangeEvent } from 'antd/lib/checkbox';
import classNames from 'classnames';
import React from 'react';

import AvatarIconPNG from '@/assets/images/members/avatar-icon.png';
import type { ThirdUser } from '@/contexts/activeRange/type';
import { concatImageUrl } from '@/utils/image';

import styles from './index.less';
import { StyledItemTitle, StyleItemLeft } from './index.style';
interface Props {
  user: ThirdUser;
  handleCheckUser: (e: CheckboxChangeEvent, user: ThirdUser) => void;
  checked: boolean;
  disabled: boolean;
}

export const UserItem: React.FC<Props> = ({ user, handleCheckUser, checked, disabled }) => {
  const avatar = user.avatar || concatImageUrl(AvatarIconPNG);

  return (
    <div className={styles['styledItem']}>
      <Checkbox
        checked={checked}
        disabled={disabled}
        onChange={(e) => {
          handleCheckUser(e, user);
        }}
      >
        <Tooltip placement="topLeft" title={disabled ? s18n('该成员已被禁用') : ''}>
          <StyleItemLeft
            className={classNames({
              disabled,
            })}
          >
            <Avatar size={24} src={avatar} />
            <StyledItemTitle>{user.name}</StyledItemTitle>
          </StyleItemLeft>
        </Tooltip>
      </Checkbox>
    </div>
  );
};
