.styledItem {
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 32px;
  margin-bottom: 4px;

  &:hover {
    background: var(--theme-menu-color-bg-hover);
  }
}

.styledItemTitle {
  color: var(--theme-text-color-default);
  font-size: 13px;
  line-height: 20px;
}

.styledAvatar {
  height: 24px;
  width: 24px;
  border-radius: 50%;
}

.styleItemLeft {
  display: flex;
  height: 32px;
  align-items: center;

  &.disabled {
    opacity: 0.4;
  }

  .styledItemTitle {
    margin-left: 8px;
  }
}
