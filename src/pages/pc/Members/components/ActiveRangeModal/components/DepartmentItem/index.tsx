import s18n from '@shimo/simple-i18n';
import { Checkbox, Tooltip } from 'antd';
import type { CheckboxChangeEvent } from 'antd/lib/checkbox';
import classNames from 'classnames';
import React from 'react';

import { ReactComponent as DepartmentIconSVG } from '@/assets/images/members/department-icon.svg';
import type { ThirdDepartment } from '@/contexts/activeRange/type';

import { StyledItem, StyledItemLinkButton, StyledItemTitle, StyledRightIcon, StyleItemLeft } from './index.style';

interface Props {
  department: ThirdDepartment;
  checked: boolean;
  handleCheckDepartment: (e: CheckboxChangeEvent, user: ThirdDepartment) => void;
  disabled: boolean;
  handleDepartmentLinkButton: (item: ThirdDepartment) => void;
}

export const DepartmentItem: React.FC<Props> = ({
  checked,
  department,
  handleCheckDepartment,
  disabled,
  handleDepartmentLinkButton,
}) => {
  return (
    <StyledItem>
      <Checkbox
        checked={checked}
        onChange={(e) => {
          handleCheckDepartment(e, department);
        }}
      >
        <StyleItemLeft>
          <DepartmentIconSVG />
          <StyledItemTitle>{department.name}</StyledItemTitle>
        </StyleItemLeft>
      </Checkbox>
      <Tooltip placement="topRight" title={disabled ? s18n('取消部门勾选可进入部门下一级') : ''}>
        <StyledItemLinkButton
          className={classNames({
            disabled,
          })}
          onClick={() => {
            if (disabled) {
              return;
            }
            handleDepartmentLinkButton(department);
          }}
        >
          <span>{s18n('下级')}</span>
          <StyledRightIcon />
        </StyledItemLinkButton>
      </Tooltip>
    </StyledItem>
  );
};
