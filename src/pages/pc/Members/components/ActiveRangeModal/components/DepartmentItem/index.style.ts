import styled from 'styled-components';

import { ReactComponent as DownIconSVG } from '@/assets/images/members/down-icon.svg';

export const StyledItem = styled.div`
  padding: 0 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 32px;
  margin-bottom: 4px;

  &:hover {
    background: var(--theme-menu-color-bg-hover);
  }
`;

export const StyleItemLeft = styled.div`
  display: flex;
  height: 32px;
  align-items: center;
`;

export const StyledItemTitle = styled.div`
  color: var(--theme-text-color-default);
  font-size: 13px;
  line-height: 20px;
  margin-left: 8px;
`;

export const StyledItemLinkButton = styled.div`
  font-size: 13px;
  line-height: 20px;
  color: var(--theme-link-button-color);
  cursor: pointer;
  display: flex;
  align-items: center;

  &:hover {
    color: var(--theme-link-button-color-hover);
  }

  &.disabled {
    color: var(--theme-link-button-color-disabled);
    cursor: not-allowed;
  }
`;

export const StyledRightIcon = styled(DownIconSVG)`
  transform: rotate(-90deg);
  flex-shrink: 0;
  margin-left: 4px;
`;
