import s18n from '@shimo/simple-i18n';
import React from 'react';
import styled from 'styled-components';

import { CustomModal } from '@/components/CustomModal';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  isPayingExpired: boolean | undefined;
}

const StyledLink = styled.a`
  color: var(--theme-link-button-color) !important;
  cursor: pointer;

  &:hover {
    color: var(--theme-link-button-color-hover) !important;
  }
`;

export const ExpiredAddMemberModal: React.FC<Props> = ({ open, changeOpen, isPayingExpired }) => {
  if (isPayingExpired === undefined) {
    return;
  }

  return (
    <CustomModal
      centered
      closable={false}
      footer={(_, { CancelBtn, OkBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      }}
      maskClosable={false}
      modalType="warning"
      okText={isPayingExpired ? s18n('续费企业版') : s18n('升级企业版')}
      open={open}
      title={s18n('操作失败')}
      width={420}
      onCancel={() => {
        changeOpen(false);
      }}
      onOk={() => {
        location.href = `${location.origin}/organization/billing`;
        changeOpen(false);
      }}
    >
      <div>
        {isPayingExpired ? s18n('您的企业版已到期，续费企业版无此限制') : s18n('您的试用已到期，升级企业版无此限制')}
      </div>
      <StyledLink href="/pricing" target="_blank">
        {s18n('了解更多企业版特权')}
      </StyledLink>
    </CustomModal>
  );
};
