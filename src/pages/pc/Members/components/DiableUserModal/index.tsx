import s18n from '@shimo/simple-i18n';
import { message, Modal } from 'antd';
import React, { useContext, useState } from 'react';
import styled from 'styled-components';

import { MembersDispatchContext } from '@/contexts/members';
import { disabledOrChangeMemberRole } from '@/contexts/members/service/api';
import type { UserItem } from '@/contexts/members/type';
import { useMeStore } from '@/store/Me';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  user: UserItem | null;
}

export const DisableUserModal: React.FC<Props> = ({ open, changeOpen, user }) => {
  const [loading, setLoading] = useState(false);
  const { refreshTableList } = useContext(MembersDispatchContext);
  const me = useMeStore((state) => state.me);

  const handleOk = async () => {
    if (!user) {
      return;
    }

    setLoading(true);
    try {
      // 1 发送移除请求
      await disabledOrChangeMemberRole({
        id: user.id,
        teamId: me!.teamId,
        role: 'disabled',
      });
      // 2 刷新列表数据 外部企业成员刷新 external
      await refreshTableList?.();
      // 3 成功操作提示
      message.success(s18n('操作成功'));
      // 4 隐藏窗口
      changeOpen(false);
    } catch (error) {
      const errorMsg = (error as any).error || s18n('未知错误');
      message.error(errorMsg);
    }
    setLoading(false);
  };

  const handleCancel = () => {
    changeOpen(false);
  };

  const StyledContext = styled.div`
    color: var(--theme-text-color-default);
    font-size: 14px;
    line-height: 22px;
  `;

  return (
    <Modal
      // modalType="warning"
      centered
      closable
      footer={(_, { CancelBtn, OkBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      }}
      maskClosable={false}
      okButtonProps={{
        loading,
      }}
      okText={s18n('确定禁用')}
      open={open}
      title={s18n('禁用成员')}
      width={420}
      onCancel={handleCancel}
      onOk={handleOk}
    >
      <StyledContext>{s18n.x`确定禁用成员"${user?.name || ''}"`}</StyledContext>
    </Modal>
  );
};
