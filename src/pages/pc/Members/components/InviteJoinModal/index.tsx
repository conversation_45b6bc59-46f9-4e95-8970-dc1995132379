import s18n from '@shimo/simple-i18n';
import { message } from 'antd';
import React, { useContext, useState } from 'react';
import styled from 'styled-components';

import { CustomModal } from '@/components/CustomModal';
import { MembersContext, MembersDispatchContext } from '@/contexts/members';
import { inviteMember } from '@/contexts/members/service/api';
import type { UserItem } from '@/contexts/members/type';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  user: UserItem | null;
}

export const StyledModalContentText = styled.div`
  color: var(--theme-text-color-default);
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 12px;
`;

// 邀请该用户加入企业 特殊 情况该用户已加入其他企业，无法邀请
export const InviteJoinModal: React.FC<Props> = ({ open, changeOpen, user }) => {
  const { outsiderPage } = useContext(MembersContext);
  const { getOutsiderMembers } = useContext(MembersDispatchContext);
  const [loading, setLoading] = useState(false);

  if (!user) {
    return;
  }

  const handleOk = async () => {
    setLoading(true);
    try {
      // 1 发送请求
      await inviteMember({ id: user.id });
      // 2 刷新数据
      await getOutsiderMembers?.({ page: outsiderPage });
      // 3 提示成功
      message.success(s18n('操作成功'));
      // 4 隐藏窗口
      changeOpen(false);
    } catch (error) {
      const errorMsg = (error as any).error || s18n('未知错误');
      message.error(errorMsg);
    }
    setLoading(false);
  };

  const handleCancel = () => {
    changeOpen(false);
  };

  return (
    <CustomModal
      centered
      closable
      footer={(_, { CancelBtn, OkBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      }}
      maskClosable={false}
      modalType="confirm"
      okButtonProps={{
        loading,
      }}
      okText={s18n('确定邀请')}
      open={open}
      title={s18n('邀请加入企业')}
      width={420}
      onCancel={handleCancel}
      onOk={handleOk}
    >
      <StyledModalContentText>{s18n.x`确定邀请“${user?.name || ''}”加入企业`}</StyledModalContentText>
    </CustomModal>
  );
};
