import { Input } from 'antd';
import styled from 'styled-components';

export const StyledLabel = styled.div`
  color: var(--theme-text-color-default);
  font-size: 14px;
  line-height: 22px;
`;

export const StyledItem = styled.div<{ $marginBottom?: number }>`
  display: flex;
  flex-direction: column;
  margin-bottom: '${({ $marginBottom = 24 }) => $marginBottom}px';

  ${StyledLabel} {
    margin-right: 8px;
  }
`;

export const StyledInput = styled(Input)`
  text-overflow: ellipsis;
`;
