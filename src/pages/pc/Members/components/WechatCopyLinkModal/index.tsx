import s18n from '@shimo/simple-i18n';
import React, { useEffect } from 'react';

import { CustomModal } from '@/components/CustomModal';
import { oldCopy } from '@/utils/copy';

import styles from './index.less';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  link: string;
}

const InfoLink = 'https://shimo.im/docs/X3kVxDgdjJyCPGPH';

export const WechatCopyLinkModal: React.FC<Props> = ({ open, changeOpen, link }) => {
  useEffect(() => {
    if (open && link) {
      oldCopy(link);
    }
  }, [open]);

  return (
    <CustomModal
      centered
      closable={false}
      footer={(_, { OkBtn }) => {
        return <OkBtn />;
      }}
      maskClosable={false}
      modalType="check"
      okText={s18n('好的')}
      open={open}
      title={s18n('已复制链接')}
      width={420}
      onOk={() => {
        changeOpen(false);
      }}
    >
      <div className={styles['styledContentText']}>{s18n('请在企业微信客户端的窗口发送链接并打开，详情请查看：')}</div>
      <a className={styles['styledLink']} href={InfoLink} rel="noreferrer" target="_blank">
        {InfoLink}
      </a>
    </CustomModal>
  );
};
