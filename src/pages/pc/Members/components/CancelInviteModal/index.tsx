import s18n from '@shimo/simple-i18n';
import { message } from 'antd';
import React, { useContext, useState } from 'react';
import styled from 'styled-components';

import { CustomModal } from '@/components/CustomModal';
import { MembersDispatchContext } from '@/contexts/members';
import { deleteMember } from '@/contexts/members/service/api';
import type { UserItem } from '@/contexts/members/type';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  user: UserItem | null;
  activeDepartmentId: number;
}

export const StyledModalContentText = styled.div`
  color: var(--theme-text-color-default);
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 12px;
`;

export const CancelInviteModal: React.FC<Props> = ({ open, changeOpen, user, activeDepartmentId }) => {
  const { refreshTableList, getSubDepartmentList } = useContext(MembersDispatchContext);
  const [loading, setLoading] = useState(false);

  const handleOk = async () => {
    if (!user) {
      return;
    }
    setLoading(true);
    try {
      // 1 发送取消请求
      await deleteMember({ id: user.id, action: 'remove' });
      // 2 刷新接口
      setTimeout(() => {
        // 3 刷新表格 按照后端的说法，删除人员和更新数据是异步操作
        // 所以删除完成后，数据可能还没有更新完成，所以需要延迟一段时间后再刷新
        Promise.all([getSubDepartmentList?.(activeDepartmentId), refreshTableList?.(activeDepartmentId)]).finally(
          () => {
            setLoading(false);
          },
        );
      }, 500);
      message?.success('操作成功');
      changeOpen(false);
    } catch (error) {
      const errorMsg = (error as any).error || s18n('未知错误');
      message?.error(errorMsg);
    }
    setLoading(false);
  };

  const handleCancel = () => {
    changeOpen(false);
  };

  return (
    <CustomModal
      centered
      closable
      footer={(_, { CancelBtn, OkBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      }}
      maskClosable={false}
      modalType="warning"
      okButtonProps={{
        loading,
      }}
      okText={s18n('确定取消')}
      open={open}
      title={s18n('取消邀请')}
      width={420}
      onCancel={handleCancel}
      onOk={handleOk}
    >
      <StyledModalContentText>{s18n`确定取消邀请“${user?.name || ''}”`}</StyledModalContentText>
    </CustomModal>
  );
};
