import s18n from '@shimo/simple-i18n';
import React from 'react';
import styled from 'styled-components';

import { CustomModal } from '@/components/CustomModal';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
}

const StyledContentText = styled.div`
  color: var(--theme-text-color-default);
  font-size: 14px;
  line-height: 22px;
`;

export const DingdingTipModal: React.FC<Props> = ({ changeOpen, open }) => {
  const handleOk = () => {
    changeOpen(false);
  };

  return (
    <CustomModal
      centered
      closable
      footer={(_, { OkBtn }) => {
        return <OkBtn />;
      }}
      maskClosable={false}
      modalType="confirm"
      open={open}
      title={s18n('温馨提示')}
      width={420}
      onCancel={handleOk}
      onOk={handleOk}
    >
      <StyledContentText>{s18n('当前版本为钉钉付费企业版')}</StyledContentText>
      <StyledContentText>{s18n('续费与增购席位请在钉钉应用市场内进行相应购买')}</StyledContentText>
    </CustomModal>
  );
};
