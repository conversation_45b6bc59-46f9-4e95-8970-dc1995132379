import s18n from '@shimo/simple-i18n';
import { message } from 'antd';
import React, { useContext, useState } from 'react';
import styled from 'styled-components';

import { CustomModal } from '@/components/CustomModal';
import { MembersDispatchContext } from '@/contexts/members';
import { deleteDepartment } from '@/contexts/members/service/api';
import type { Department, ItemSource } from '@/contexts/members/type';

interface Props {
  department: Department;
  open: boolean;
  changeOpen: (open: boolean) => void;
  changeTreeFocus?: (department: ItemSource, parentId: number) => void;
}

const StyledModalContentText = styled.div`
  color: var(--theme-text-color-default);
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 12px;
`;

export const DeleteDepartmentModal: React.FC<Props> = ({ department, open, changeOpen, changeTreeFocus }) => {
  const { getSubDepartmentList } = useContext(MembersDispatchContext);
  const [loading, setLoading] = useState(false);

  const handleOk = async () => {
    setLoading(true);

    try {
      // 1. 发送删除请求
      await deleteDepartment({ id: department.id });
      // 2. 焦点判断
      const { parentId } = department;
      //  更改左侧树的焦点
      changeTreeFocus?.(department as ItemSource, parentId!);
      // 3. 更新删除树，通过更新当前节点的父节点来完成
      await getSubDepartmentList?.(parentId!);
      message.success(s18n('操作成功'));
      changeOpen(false);
    } catch (error) {
      const errorMsg = (error as any)?.error || s18n('操作失败');
      message.error(errorMsg);
    }
    setLoading(false);
  };

  const handleCancel = () => {
    changeOpen(false);
  };

  return (
    <CustomModal
      centered
      closable
      footer={(_, { OkBtn, CancelBtn }) => (
        <>
          <OkBtn />
          <CancelBtn />
        </>
      )}
      maskClosable={false}
      modalType="warning"
      okButtonProps={{
        loading,
        danger: true,
      }}
      okText={s18n('确定删除')}
      open={open}
      title={s18n('删除部门')}
      width={420}
      onCancel={handleCancel}
      onOk={handleOk}
    >
      <StyledModalContentText>{s18n`确定要删除“${department.name}”`}</StyledModalContentText>
    </CustomModal>
  );
};
