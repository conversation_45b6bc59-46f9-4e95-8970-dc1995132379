import s18n from '@shimo/simple-i18n';
import { Checkbox, Input, message, Tooltip } from 'antd';
import React, { useContext, useEffect, useState } from 'react';

import EmptyPNG from '@/assets/images/members/empty-holder.png';
import loadingGIF from '@/assets/images/members/loading-gif.gif';
import { MembersContext, MembersDispatchContext } from '@/contexts/members';
import { addMembersByIds, getMembers, searchUsers } from '@/contexts/members/service/api';
import { type MimeMembersResponse, type SearchUser, TeamRole } from '@/contexts/members/type';
import { concatImageUrl } from '@/utils/image';

import { getDepartmentById } from '../../utils';
import {
  EmptyIMG,
  LoadingIMG,
  StyledAvatar,
  StyledContent,
  StyledDisableTag,
  StyledEmail,
  StyledHolderContainer,
  StyledItem,
  StyledList,
  StyledModal,
  StyledModal<PERSON>eader,
  StyledName,
  StyledNameContainer,
  StyledSelected,
  StyledText,
  StyledTitle,
} from './index.style';

const { Search } = Input;

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  activeDepartmentId: number;
}

const PageSize = 20;
const RowHeight = 32;

export const AddNewMemberModal: React.FC<Props> = ({ changeOpen, open, activeDepartmentId }) => {
  const { departmentUserIDs, treeDataSource } = useContext(MembersContext);
  const { refreshTableList, getSubDepartmentList } = useContext(MembersDispatchContext);
  const [isInitLoading, setIsInitLoading] = useState(false);
  const [isLoadMoreLoading, setIsLoadMoreLoading] = useState(false); // 加载更多 用户底部下拉加载

  const [isEmpty, setIsEmpty] = useState(false);
  const [userList, setUserList] = useState<MimeMembersResponse>([]);
  const [keyword, setKeyword] = useState('');
  const [isInSearchMode, setIsInSearchMode] = useState(false);
  const [searchList, setSearchList] = useState<SearchUser[]>([]);
  const [searchNext, setSearchNext] = useState(-1);

  const [page, setPage] = useState(1);
  const [hasNextPage, setHasNextPage] = useState(false);

  const [currentCheckIds, setCurrentCheckIds] = useState<number[]>([]);
  const activeDeparment = getDepartmentById(treeDataSource, activeDepartmentId);
  const [loading, setLoading] = useState(false);

  /**
   * 初始化成员列表
   */
  const initMemberList = async () => {
    setIsInitLoading(true);
    setUserList([]);
    try {
      const res = await getMembers(1, PageSize);
      setUserList(res);
      setPage(1);
      if (res && res.length) {
        if (res.length === 0) {
          setIsEmpty(true);
        } else {
          if (res.length < PageSize) {
            setHasNextPage(false);
          } else {
            setHasNextPage(true);
          }
          setIsEmpty(false);
        }
      }
    } catch (error) {
      console.error('getMemberList ->', error);
    }
    setIsInitLoading(false);
  };

  /**
   * 加载更多成员列表
   */
  const loadMoreUsers = async () => {
    if (isInitLoading || isLoadMoreLoading || !hasNextPage) {
      return;
    }

    setIsLoadMoreLoading(true);
    setIsInitLoading(false);
    try {
      const res = await getMembers(page + 1, PageSize);
      if (res.length < PageSize) {
        setHasNextPage(false);
      } else {
        setHasNextPage(true);
      }

      setUserList(userList.concat(res));
      setPage(page + 1);
    } catch (error) {
      console.error('loadMoreUsers ->', error);
    }
    setIsLoadMoreLoading(false);
  };

  /**
   * 搜索用户列表
   */
  const handleSearch = async (keyword: string) => {
    setKeyword(keyword);
    setSearchList([]);
    if (!keyword) {
      initMemberList();
      setIsInSearchMode(false);
      return;
    } else {
      setIsInSearchMode(true);
    }

    setIsInitLoading(true);
    setIsLoadMoreLoading(false);
    try {
      const response = await searchUsers({ keyword, limit: PageSize });
      const { next, results } = response.data;
      const list = results.map((item) => item.user);

      setSearchNext(next);
      setSearchList(list);
      if (list.length === 0) {
        setIsEmpty(true);
      } else {
        setIsEmpty(false);
      }
    } catch (error) {
      console.error('handleSearch ->', error);
    }
    setIsInitLoading(false);
  };

  /**
   * 加载更多搜索用户
   */
  const loadMoreSearchUsers = async () => {
    if (isInitLoading || isLoadMoreLoading || searchNext === -1) {
      return;
    }

    setIsLoadMoreLoading(true);
    setIsInitLoading(false);
    try {
      const response = await searchUsers({
        keyword,
        limit: PageSize,
        next: searchNext,
      });
      const { next, results } = response.data;
      const list = results.map((item) => item.user);

      setSearchNext(next);
      setSearchList(searchList.concat(list));
    } catch (error) {
      console.error('loadMoreSearchUsers ->', error);
    }
    setIsLoadMoreLoading(false);
  };

  // 是否在当前的选中列表
  // 是否在企业列表
  const handleCheckBox = ({ checked, id }: { checked: boolean; id: number }) => {
    if (checked && !currentCheckIds.includes(id)) {
      setCurrentCheckIds([...currentCheckIds, id]);
    }

    if (!checked && currentCheckIds.includes(id)) {
      setCurrentCheckIds(currentCheckIds.filter((item) => item !== id));
    }
  };

  useEffect(() => {
    if (open) {
      initMemberList();
    }
  }, [open]);

  const list = isInSearchMode ? searchList : userList;

  const handleOk = async () => {
    if (!(currentCheckIds.length > 0)) {
      return;
    }

    if (currentCheckIds.length > 100) {
      message.error(s18n('单次最多添加 100 人，请多次添加'));
      return;
    }

    setLoading(true);
    try {
      // 1. 提交选中用户 ID
      await addMembersByIds({
        departmentIDs: [activeDepartmentId],
        userIDs: currentCheckIds,
      });

      // 2. 更新当前部门的信息
      await getSubDepartmentList?.(activeDepartmentId);
      // 3. 更新当前部门的用户列表
      await refreshTableList?.(activeDepartmentId);
      message.success(s18n`成功添加 ${currentCheckIds.length} 人`);
      changeOpen(false);
    } catch (error) {
      const errorMsg = (error as any).error || s18n('操作失败');
      message.error(errorMsg);
    }

    setLoading(false);
  };
  if (!activeDeparment) {
    return null;
  }

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    if (scrollTop + clientHeight >= scrollHeight - RowHeight) {
      // 滚动到底部
      if (isInSearchMode && searchNext > 0 && !isLoadMoreLoading) {
        loadMoreSearchUsers();
      } else if (!isInSearchMode && hasNextPage && !isLoadMoreLoading) {
        loadMoreUsers();
      }
    }
  };

  return (
    <StyledModal
      centered
      closable
      footer={(_, { OkBtn }) => {
        return <OkBtn />;
      }}
      maskClosable={false}
      okButtonProps={{
        disabled: currentCheckIds.length < 1,
        style: {
          overflow: 'hidden',
          maxWidth: '50%',
        },
        loading,
      }}
      okText={s18n`添加到${activeDeparment?.name}`}
      open={open}
      title={s18n('添加新成员')}
      width={550}
      onCancel={() => {
        changeOpen(false);
      }}
      onOk={handleOk}
    >
      <StyledModalHeader>
        <StyledTitle>{s18n('请选择要添加的企业成员')}</StyledTitle>
        {currentCheckIds.length > 0 ? (
          <StyledSelected>{s18n`当前已选中（${currentCheckIds.length}）`}</StyledSelected>
        ) : (
          <div />
        )}
      </StyledModalHeader>
      <StyledContent>
        <Search allowClear onSearch={handleSearch} />
        {!isInitLoading && !isEmpty && (
          <StyledList onScroll={handleScroll}>
            {list.map((item) => {
              // 已经在部门了
              const alreadyIn = departmentUserIDs.includes(item.id);
              // 零时选取的
              const justIn = currentCheckIds.includes(item.id);

              return (
                <StyledItem key={item.id}>
                  <StyledNameContainer>
                    <StyledAvatar src={item.avatar} />
                    <StyledName>
                      <Tooltip placement="topLeft" title={item.name}>
                        <span>{item.name}</span>
                      </Tooltip>
                    </StyledName>
                    {item.teamRole === TeamRole.disabled && <StyledDisableTag>{s18n('禁用')}</StyledDisableTag>}
                  </StyledNameContainer>
                  <StyledEmail>{item.email}</StyledEmail>
                  <Checkbox
                    checked={alreadyIn || justIn}
                    disabled={alreadyIn || item.teamRole === TeamRole.disabled}
                    onChange={(e) => {
                      handleCheckBox({
                        id: item.id,
                        checked: e.target.checked,
                      });
                    }}
                  />
                </StyledItem>
              );
            })}
          </StyledList>
        )}
        {isInitLoading && (
          <StyledHolderContainer>
            <LoadingIMG src={concatImageUrl(loadingGIF)} />
          </StyledHolderContainer>
        )}
        {!isInitLoading && isEmpty && (
          <StyledHolderContainer>
            <div>
              <EmptyIMG src={concatImageUrl(EmptyPNG)} />
              <StyledText>{s18n('无搜索结果')}</StyledText>
            </div>
          </StyledHolderContainer>
        )}
      </StyledContent>
    </StyledModal>
  );
};
