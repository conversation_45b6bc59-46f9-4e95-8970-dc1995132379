import s18n from '@shimo/simple-i18n';
import { message } from 'antd';
import React, { useContext, useState } from 'react';

import { CustomModal } from '@/components/CustomModal';
import { MembersDispatchContext } from '@/contexts/members';
import { disabledOrChangeMemberRole } from '@/contexts/members/service/api';
import type { UserItem } from '@/contexts/members/type';

import styles from './index.less';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  user: UserItem | null;
  isReactive: boolean;
  activeDepartmentId: number;
}

// 1. 禁用 成员禁用 // 可能出现在禁用列表 和 成员角色本身已经禁用的情况下 重新激活
// 2. 未激活列表 直接激活

export const ReactivatedModal: React.FC<Props> = ({
  changeOpen,
  open,
  user,
  isReactive = true,
  activeDepartmentId,
}) => {
  const [loading, setLoading] = useState(false);
  const { refreshTableList } = useContext(MembersDispatchContext);

  const handelCancel = () => {
    changeOpen(false);
  };

  const handleOK = async () => {
    if (!user) {
      return;
    }
    setLoading(true);
    try {
      // 1. 发送激活角色请求
      await disabledOrChangeMemberRole({
        id: user?.id,
        teamId: user?.id,
        role: isReactive ? 'member' : 'seat',
      });

      // 2. 刷新用户列表
      await refreshTableList?.(activeDepartmentId);
      message.success(s18n('操作成功'));
      changeOpen(false);
    } catch (error) {
      const errorMsg = (error as any).error || s18n('未知错误');
      message.error(errorMsg);
    }
    setLoading(false);
  };

  return (
    <CustomModal
      centered
      closable
      footer={(_, { OkBtn, CancelBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      }}
      maskClosable={false}
      modalType="confirm"
      okButtonProps={{
        loading,
      }}
      okText={s18n('重新激活')}
      open={open}
      title={isReactive ? s18n('重新激活成员') : s18n('激活成员')}
      width={420}
      onCancel={handelCancel}
      onOk={handleOK}
    >
      <div className={styles['styledContentText']}>
        {isReactive ? s18n.x`确定重新激活成员“${user?.name || ''}”` : s18n.x`确定激活成员“${user?.name || ''}”`}
      </div>
    </CustomModal>
  );
};
