import s18n from '@shimo/simple-i18n';
import { message } from 'antd';
import React, { useContext, useState } from 'react';

import { CustomModal } from '@/components/CustomModal';
import { AdminModeDispatchContext } from '@/contexts/AdminMode';
import { MembersDispatchContext } from '@/contexts/members';
import { unbindEmail } from '@/contexts/members/service/api';
import type { UserItem } from '@/contexts/members/type';

import styles from './index.less';

interface Props {
  user: UserItem | null;
  open: boolean;
  changeOpen: (v: boolean) => void;
  activeDepartmentId: number;
}

export const UnBindEmailModal: React.FC<Props> = ({ user, open, changeOpen, activeDepartmentId }) => {
  const { openAuthModal } = useContext(AdminModeDispatchContext);
  const { refreshTableList } = useContext(MembersDispatchContext);

  const [loading, setLoading] = useState(false);

  const unbindEmailAndRefresh = async () => {
    if (!user) {
      return;
    }
    try {
      await unbindEmail({
        teamId: user.teamId,
        userId: user.id,
      });
      await refreshTableList?.(activeDepartmentId);
      message.success(s18n('操作成功'));
    } catch (error) {
      return Promise.reject(await error);
    }
  };

  const handelOK = async () => {
    setLoading(true);
    try {
      await unbindEmailAndRefresh();
      changeOpen(false);
    } catch (error) {
      const errorCode = (error as any)?.code;

      const errorMsg = (error as any)?.msg || s18n('未知错误');
      if (errorCode === 17004) {
        // 管控模式授权失败
        // 启动特殊授权模式
        openAuthModal?.({
          onSuccess: () => {
            unbindEmailAndRefresh().finally();
          },
        });
        changeOpen(false);
      }

      if (errorMsg) {
        message?.error(errorMsg);
      }
    }
    setLoading(false);
  };

  return (
    <CustomModal
      centered
      closable
      cancelText={s18n('暂不修改')}
      footer={(_, { OkBtn, CancelBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      }}
      maskClosable={false}
      modalType="warning"
      okButtonProps={{
        loading,
      }}
      okText={s18n('确定删除')}
      open={open}
      title={s18n('删除已绑定邮箱')}
      width={420}
      onCancel={() => {
        changeOpen(false);
      }}
      onOk={handelOK}
    >
      <div className={styles['styledContentText']}>
        {s18n('删除绑定后，原有账号通过邮箱登录的方式将失效。确定删除吗？')}
      </div>
    </CustomModal>
  );
};
