import s18n from '@shimo/simple-i18n';
import { Button, Form, Input, message } from 'antd';
import React, { useState } from 'react';

import { addPDMembers } from '@/contexts/members/service/api';
import { useMeStore } from '@/store/Me';
import { isEmail } from '@/utils/validate';

import { StyledAddMemberHeader, StyledTitle } from './index.style';

export interface AddPdUserItem {
  name: string;
  email: string;
  id: number;
  password: string;
}

export enum AddPDStatus {
  form = 'form',
  result = 'result',
}

interface Props {
  users: AddPdUserItem[];
  setUsers: (v: AddPdUserItem[]) => void;
  setAddStatus: (v: AddPDStatus) => void;
}

const Name = 'name';
const Email = 'email';

export const AddMemberForm: React.FC<Props> = ({ users, setUsers, setAddStatus }) => {
  const me = useMeStore((state) => state.me);

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const addAccounts = async () => {
    if (loading) {
      return;
    }

    // 使用 input 的数据
    try {
      const validateData = await form.validateFields();
      const response = await addPDMembers({
        ...validateData,
        teamId: me!.teamId,
      });
      // 1 添加用户成功
      setUsers([
        ...users,
        {
          ...validateData,
          id: users.length + 1,
          password: response.password ?? '',
        },
      ]);
      message.success('添加成功');
      form.resetFields();
      // 2. 如果返回了密码
      if (response.password) {
        // 3. 展示结果页面
        setAddStatus(AddPDStatus.result);
      }
    } catch (error) {
      const {
        data: { code, msg },
      } = error as any;
      if (code) {
        message.error(msg);
      }
      return;
    }

    setLoading(false);
  };

  return (
    <>
      <StyledAddMemberHeader>
        <StyledTitle>{s18n('请输入成员姓名和邮箱，添加后成员会收到带有初始密码的邮件。')}</StyledTitle>
      </StyledAddMemberHeader>

      <div>
        <Form form={form} layout="vertical" requiredMark={false} validateTrigger="onBlur">
          <Form.Item
            key={Name}
            label={s18n`姓名`}
            name={Name}
            rules={[
              {
                required: true,
                message: s18n('请输入姓名'),
              },
            ]}
            validateFirst={true}
          >
            <Input maxLength={20} placeholder={s18n('请输入成员邮箱或手机号')} />
          </Form.Item>
          <Form.Item
            key={Email}
            label={s18n`邮箱`}
            name={Email}
            rules={[
              {
                required: true,
                message: s18n('请输入成员邮箱'),
              },
              {
                validator: (_, value) => {
                  if (isEmail(value)) {
                    return Promise.resolve();
                  } else {
                    return Promise.reject(s18n('请输入正确的邮箱'));
                  }
                },
              },
            ]}
            validateFirst={true}
          >
            <Input placeholder={s18n('请输入成员邮箱')} />
          </Form.Item>
        </Form>
        <Button
          loading={loading}
          type="primary"
          onClick={() => {
            addAccounts();
          }}
        >
          {s18n('添加并继续')}
        </Button>
      </div>
    </>
  );
};
