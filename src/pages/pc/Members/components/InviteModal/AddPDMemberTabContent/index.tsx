import s18n from '@shimo/simple-i18n';
import { Button } from 'antd';
import React, { useState } from 'react';

import { AddMemberForm, AddPDStatus, type AddPdUserItem } from './components/AddMemberForm';
import { AddMemberResult } from './components/AddMemberResult';
import styles from './index.less';

interface Props {
  changeOpen: (v: boolean) => void;
  visible: boolean;
}

export const AddPDMemberTabContent: React.FC<Props> = ({ changeOpen, visible }) => {
  const [addStatus, setAddStatus] = useState<AddPDStatus>(AddPDStatus.form);
  const [pdUsers, setPdUsers] = useState<AddPdUserItem[]>([]);

  const lastUser = pdUsers[pdUsers.length - 1] ?? {
    name: '',
    email: '',
    id: 0,
    password: '',
  };

  return (
    <>
      {visible && (
        <div className={styles.styledContent}>
          {addStatus === AddPDStatus.form ? (
            <AddMemberForm setAddStatus={setAddStatus} setUsers={setPdUsers} users={pdUsers} />
          ) : (
            <AddMemberResult setAddStatus={setAddStatus} user={lastUser} />
          )}
        </div>
      )}
      <div
        style={{
          marginTop: 12,
          marginBottom: -12,
          display: visible ? 'flex' : 'none',
          flexDirection: 'row-reverse',
        }}
      >
        <Button
          onClick={() => {
            // TODO
            changeOpen(false);
          }}
        >
          {s18n`完成`}
        </Button>
      </div>
    </>
  );
};
