import React, { useMemo, useState } from 'react';

import { fm2 } from '@/modules/Locale';

import { AddMemberTabContent } from './AddMemberTabContent';
import { AddPDMemberTabContent } from './AddPDMemberTabContent/index';
import { BatchAddMemberTabContent } from './BatchAddMemberTabContent';
import { StyledModal } from './index.style';
import { InviteLinkTabContent } from './InviteLinkTabContent';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  activeDepartmentId: number;
  inviteType: InviteType;
}

export type InviteType = 'addMember' | 'addMemberByOrder' | 'batchAddMember' | 'inviteLink';

export enum InviteTabKeys {
  /** 添加成员 */
  addMember = 'addMember',
  /** 依次添加成员 */
  addMemberByOrder = 'addMemberByOrder',
  /** 批量导入成员 */
  batchAddMember = 'batchAddMember',
  /** 邀请链接 */
  inviteLink = 'inviteLink',
}

export const InviteModal: React.FC<Props> = ({ open, changeOpen, inviteType, activeDepartmentId }) => {
  const [activeTabId] = useState<InviteType>(inviteType);

  const title = useMemo(() => {
    switch (inviteType) {
      case 'addMember':
        return fm2('Members.addMember');
      case 'addMemberByOrder':
        return fm2('Members.addMember');
      case 'batchAddMember':
        return fm2('Members.batchAddMember');
      case 'inviteLink':
        return fm2('Members.inviteLink');
      default:
        return fm2('Members.addMember');
    }
  }, [inviteType]);

  return (
    <StyledModal
      centered
      closable
      footer={() => {
        return null;
      }}
      height={500}
      open={open}
      title={title}
      width={618}
      onCancel={() => {
        changeOpen(false);
      }}
    >
      {activeTabId === InviteTabKeys.addMemberByOrder && (
        <AddPDMemberTabContent changeOpen={changeOpen} visible={activeTabId === InviteTabKeys.addMemberByOrder} />
      )}
      {activeTabId === InviteTabKeys.addMember && (
        <AddMemberTabContent
          activeDepartmentId={activeDepartmentId}
          changeOpen={changeOpen}
          visible={activeTabId === InviteTabKeys.addMember}
        />
      )}
      <BatchAddMemberTabContent visible={activeTabId === InviteTabKeys.batchAddMember} />
      {<InviteLinkTabContent visible={activeTabId === InviteTabKeys.inviteLink} />}
    </StyledModal>
  );
};
