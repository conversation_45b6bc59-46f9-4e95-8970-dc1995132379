import { Space } from 'antd';
import React, { useContext, useEffect, useState } from 'react';

import { fm, fm2 } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

import { MembersContext, MembersDispatchContext, MembersProvider } from '../../../contexts/members';
import { getReminder, loadExternal, loadInactivatedMembers } from '../../../contexts/members/service/api';
import { PermissionContext } from '../../../contexts/permissions';
import { isCreator } from '../../../contexts/permissions/helper';
import { ReminderModal } from './components/ReminderModal';
import { RootId } from './const';
import styles from './index.less';
import { LeftPart } from './LeftPart';
import { RightPart } from './RightPart';

export const Members: React.FC = () => {
  const me = useMeStore((state) => state.me);

  const team = me?.team;
  const { isDingtalk, isSSOSAML, isWework } = useContext(PermissionContext);
  const [activeDepartmentId, setActiveDepartmentId] = useState(RootId);
  const { dispatch, getSubDepartmentList, getDepartmentUserList } = useContext(MembersDispatchContext);
  const { treeDataSourceLoading, usersLoading } = useContext(MembersContext);
  const [reminderOpen, setReminderOpen] = useState(false);
  const [organizationMessage, setOrganizationMessage] = useState('');
  const [initLoading, setInitLoading] = useState(false);
  const [isInit, setIsInit] = useState(true);

  useEffect(() => {
    if (!initLoading && isInit && (treeDataSourceLoading || usersLoading)) {
      setInitLoading(true);
    }

    if (initLoading && !(treeDataSourceLoading || usersLoading)) {
      setInitLoading(false);
      setIsInit(false);
    }
  }, [treeDataSourceLoading, usersLoading]);

  //@ts-ignore
  const changeActiveDepartmentId = (activeId: number) => {
    setActiveDepartmentId(activeId);
  };

  useEffect(() => {
    getSubDepartmentList?.(activeDepartmentId).catch((error) => {
      console.error('getSubDepartmentList', error);
    });
    getDepartmentUserList?.({
      departmentId: activeDepartmentId,
      page: 1,
    });
  }, [activeDepartmentId]);

  const initInactiveMembersAndExternal = async (teamId: number) => {
    try {
      const { users } = await loadInactivatedMembers({ teamId });
      dispatch?.({
        type: 'setUnactivated',
        payload: {
          unactivated: users,
        },
      });
      if (isDingtalk || isWework || isSSOSAML) {
        const externalList = await loadExternal({ teamId });
        dispatch?.({
          type: 'setExternal',
          payload: {
            external: externalList,
          },
        });
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (team?.id) {
      initInactiveMembersAndExternal(team.id);
    }
  }, [team, isDingtalk, isDingtalk, isSSOSAML]);

  useEffect(() => {
    if (team?.id) {
      getReminder(team.id).then((res) => {
        if (res.message && res.message !== '') {
          setOrganizationMessage(res.message);
          setReminderOpen(true);
        }
      });
    }
  }, [team]);

  return (
    <div className={styles['styledMainContent']}>
      <Space className={styles['styleTopHeader']}>
        <span className={styles['styleTopHeaderName']}>{fm('Management.memberList')}</span>
        <span className={styles['styleTopHeaderId']}>
          {team.name} / {fm('Members.teamId', { id: team.id })}
        </span>
      </Space>
      <div className={styles['styledContent']}>
        <LeftPart activeDepartmentId={activeDepartmentId} changeActiveDepartmentId={changeActiveDepartmentId} />
        <RightPart
          activeDepartmentId={activeDepartmentId}
          changeActiveDepartmentId={changeActiveDepartmentId}
          initLoading={initLoading}
        />
        {reminderOpen && me && isCreator(me) && (
          <ReminderModal changeOpen={setReminderOpen} open={reminderOpen} organizationMessage={organizationMessage} />
        )}
      </div>
    </div>
  );
};

export const MembersWithContext: React.FC = () => {
  useEffect(() => {
    document.title = fm2('Management.memberList');
  }, []);
  return (
    <MembersProvider>
      <Members />
    </MembersProvider>
  );
};
