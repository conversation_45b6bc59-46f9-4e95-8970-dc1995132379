/* eslint-disable complexity */
import s18n from '@shimo/simple-i18n';
import { message, Modal } from 'antd';
import React, { useContext, useEffect, useState } from 'react';

// import {
//   FileHandoverHistoryModal,
//   FileHandoverModal,
// } from '../../components/HandoverModal';
import type { Me } from '@/model/Me';
import { useMeStore } from '@/store/Me';

// import { isDingtalk, isWework } from '@shimo/knife/browser';
// import { getString } from '@shimo/runtime-env';
import { AdminModeContext, AdminModeDispatchContext } from '../../../../contexts/AdminMode';
import { isEnterprise, isExpired, isTrial } from '../../../../contexts/me/util';
import { MembersContext, MembersDispatchContext } from '../../../../contexts/members';
import { deleteMember } from '../../../../contexts/members/service/api';
import {
  OtherListKeys,
  // TeamRole,
  type UserItem,
} from '../../../../contexts/members/type';
import { PermissionContext } from '../../../../contexts/permissions';
import type { Department } from '../../../../contexts/suit/type';
import { ActiveOrDisableModal } from '../components/ActiveOrDisableModal';
import { ActiveRangeModalWithProvider } from '../components/ActiveRangeModal';
import { AddNewMemberModal } from '../components/AddNewMember';
import { AddSubDepartmentModal } from '../components/AddSubDepartment ';
import { BindEmailModal } from '../components/BindEmailModal';
import { CancelInviteModal } from '../components/CancelInviteModal';
import { CantDeleteModal } from '../components/CantDeleteModal';
import { ChangeDepartmentModal } from '../components/ChangeDepartment';
import { CheckFileModal } from '../components/CheckFileModal';
import { DeleteDepartmentModal } from '../components/DeleteDepartment';
import { DeleteMemberModal } from '../components/DeleteMemberModal';
import { DisableUserModal } from '../components/DiableUserModal';
import { DingdingTipModal } from '../components/DingdingTipModal';
import { EditDepartmentNameModal } from '../components/EditDepartmentNameModal';
import { EditNickNameModal } from '../components/EditNickNameModal';
import { ExpiredAddMemberModal } from '../components/ExpiredAddMemberModal';
import { InviteJoinModal } from '../components/InviteJoinModal';
import type { InviteType } from '../components/InviteModal';
import { InviteModal } from '../components/InviteModal';
import { ReactivatedModal } from '../components/ReactivateModal';
import { RemoveFromDepartmentModal } from '../components/RemoveFromDepartmentModal';
import { RemoveOutersModal } from '../components/RemoveOutersModal';
import { RestPasswordModal } from '../components/RestPasswordModal';
import { UnBindEmailModal } from '../components/UnBindEmailModal';
import { WechatCopyLinkModal } from '../components/WechatCopyLinkModal';
import { MemberMenuKeys } from '../menus';
import { getDepartmentById } from '../utils';
import { DepartmentTable } from './components/DepartmentTable/DepartmentTable';
import { OperationContainer } from './components/OperationContainer/OperationContainer';
import { OuterTable } from './components/OuterTable/OuterTable';
import { RightHeader } from './components/RightHeader/RightHeader';
import styles from './index.less';

interface Props {
  changeActiveDepartmentId: (v: number) => void;
  activeDepartmentId: number;
  initLoading: boolean;
}

export const RightPart: React.FC<Props> = ({ changeActiveDepartmentId, activeDepartmentId, initLoading }) => {
  const {
    activeOtherKey,
    isInSearchMode,
    searchUserList,
    treeDataSource,
    onlyOnRootTotal,
    outsiderTotal,
    disableTotal,
    usersPage,
    users,
    departmentUserIDs,
    total,
    onlyOnRootList,
    onlyOnRootPage,
    outsiderList,
    outsiderPage,
    disableList,
    disablePage,
    unactivated,
  } = useContext(MembersContext);
  const { dispatch } = useContext(MembersDispatchContext);
  const { authSuccess } = useContext(AdminModeContext);
  const { openAuthModal } = useContext(AdminModeDispatchContext);
  const { syncPanelEnabled } = useContext(PermissionContext);
  const me = useMeStore((state) => state.me);
  const [modal, contextHolder] = Modal.useModal();
  const [addNewMemberOpen, setAddNewMemberOpen] = useState(false);
  const [inviteOpen, setInviteOpen] = useState(false);
  const [inviteType, setInviteType] = useState<InviteType>('addMember');
  const [changeDepartmentOpen, setChangeDepartmentOpen] = useState(false);
  const [editNickNameOpen, setEditNickNameOpen] = useState(false);
  const [checkFileOpen, setCheckFileOpen] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<UserItem[]>([]);
  const activeDepartment = getDepartmentById(treeDataSource, activeDepartmentId);
  const [currentTableUser, setCurrentTableUser] = useState<UserItem | null>(null);
  const [currentTableDepartment, setCurrentTableDepartment] = useState<Department | null>(null);

  const [bindEmailOpen, setBindEmailOpen] = useState(false);
  const [restPasswordOpen, setRestPasswordOpen] = useState(false);
  const [deleteMemberOpen, setDeleteMemberOpen] = useState(false);
  const [unbindEmailOpen, setUnbindEmailOpen] = useState(false);
  const [cancelInviteOpen, setCancelInviteOpen] = useState(false);
  const [inviteJoinOpen, setInviteJoinOpen] = useState(false);
  const [reactiveOpen, setReactiveOpen] = useState(false);
  const [activateOpen, setActivateOpen] = useState(false);

  const [removeOutersOpen, setRemoveOutersOpen] = useState(false);
  const [activateOrDisableOpen, setActivateOrDisableOpen] = useState(false);
  const [activeRangeOpen, setActiveRangeOpen] = useState(false);
  // const [fileHandoverOpen, setFileHandoverOpen] = useState(false);
  // const [fileHandoverHistoryOpen, setFileHandoverHistoryOpen] = useState(false);
  const [addSubDepartmentOpen, setAddSubDepartmentOpen] = useState(false);
  const [editNameOpen, setEditNameOpen] = useState(false);
  const [deleteDepartmentOpen, setDeleteDepartmentOpen] = useState(false);
  const [cantDeleteOpen, setCantDeleteOpen] = useState(false);
  const [dingdingTipOpen, setDingdingTipOpen] = useState(false);
  const [disableUserOpen, setDisableUserOpen] = useState(false);
  const [expiredAddMemberOpen, setExpiredAddMemberOpen] = useState(false);
  const [wechatCopyLinkOpen, setWechatCopyLinkOpen] = useState(false);
  const [wechatDownloadLink, setWechatDownloadLink] = useState('');
  const [removeFromDepartmentOpen, setRemoveFromDepartmentOpen] = useState(false);
  // 付费过期，而非试用版过期
  const [isPayingExpired, setIsPayingExpired] = useState<boolean | undefined>(undefined);

  const checkAddNewMemberPermissions = (currentUser: Me) => {
    // 是否通过过期验证
    let pass = true;
    // 企业版添加成员限制
    if (isEnterprise(currentUser)) {
      if (isExpired(currentUser)) {
        if (isTrial(currentUser)) {
          setIsPayingExpired(false);
        } else {
          setIsPayingExpired(true);
        }
        // 此处认为没有通过过期验证
        pass = false;
      }
    }

    return pass;
  };

  useEffect(() => {
    // 切换部门时,清空选中的内容
    setSelectedUsers([]);
  }, [activeDepartmentId]);

  const removeFromDepartment = () => {
    setRemoveFromDepartmentOpen(true);
  };

  const deleteRecord = async (id: number) => {
    try {
      await deleteMember({
        id,
        action: 'remove',
      });
      message.success(s18n`操作成功`);

      if (isInSearchMode) {
        dispatch?.({
          type: 'setSearchResult',
          payload: {
            searchUserList: searchUserList.filter((item) => item.id !== id),
          },
        });
      } else if (!activeOtherKey) {
        // 当前在全部成员模式
        dispatch?.({
          type: 'setUserResponse',
          payload: {
            users: users.filter((item) => item.id !== id),
            departmentUserIDs: departmentUserIDs.filter((item) => item !== id),
            total: total - 1,
            usersPage,
          },
        });
      } else if (activeOtherKey === OtherListKeys.OnlyOnRoot) {
        // 未分配部门
        dispatch?.({
          type: 'setOnlyOnRoot',
          payload: {
            onlyOnRootTotal: onlyOnRootTotal - 1,
            onlyOnRootList: onlyOnRootList.filter((item) => item.id !== id),
            onlyOnRootPage: onlyOnRootPage,
          },
        });
      } else if (activeOtherKey === OtherListKeys.OuterSider) {
        // 外部协作者
        dispatch?.({
          type: 'setOutsider',
          payload: {
            outsiderTotal: outsiderTotal - 1,
            outsiderList: outsiderList.filter((item) => item.id !== id),
            outsiderPage: outsiderPage,
          },
        });
      } else if (activeOtherKey === OtherListKeys.Disabled) {
        // 禁用成会员
        dispatch?.({
          type: 'setDisable',
          payload: {
            disableTotal: disableTotal - 1,
            disableList: disableList.filter((item) => item.id !== id),
            disablePage: disablePage,
          },
        });
      } else if (activeOtherKey === OtherListKeys.Inactivated) {
        // 未激活
        dispatch?.({
          type: 'setUnactivated',
          payload: {
            unactivated: unactivated.filter((item) => item.id !== id),
          },
        });
      }
    } catch (error) {
      const errorMsg = (error as any)?.error || s18n('未知错误');
      message.error(errorMsg);
      console.error('deleteRecord', error);
    }
  };

  const handleContextMenuClick = ({ key, record }: { key: string; record: UserItem | Department }) => {
    // const featureDisabled =
    //   !!getString('SELF_HOSTED_CUSTOM_LOGIN') || isDingtalk() || isWework();
    if (key === MemberMenuKeys.addSubDepartment) {
      // 添加子部门
      setAddSubDepartmentOpen(true);
    } else if (key === MemberMenuKeys.accountSettings) {
      // 账号设置
      location.href = '/profile/accountinfo';
    } else if (key === MemberMenuKeys.activateMember) {
      // 激活成员
      setActivateOpen(true);
    } else if (key === MemberMenuKeys.bindEmail) {
      // 绑定邮箱
      // if (featureDisabled) {
      //   return;
      // }
      // 1. 如果没有进入管理员模式
      // 2. 打开授权对话框 1 手机验证码 2 邮箱验证 3 密码验证
      if (!authSuccess) {
        openAuthModal?.({
          onSuccess: () => {
            setBindEmailOpen(true);
          },
        });
      } else {
        // 打开绑定邮箱对话框
        setBindEmailOpen(true);
      }
    } else if (key === MemberMenuKeys.cancelInvite) {
      // 取消邀请
      setCancelInviteOpen(true);
    } else if (key === MemberMenuKeys.checkCollaboratedFile) {
      // 查看该用户参与协作的企业文件
      setCheckFileOpen(true);
    } else if (key === MemberMenuKeys.deleteDepartment) {
      // 删除部门
      if (currentTableDepartment && currentTableDepartment.allMemberCount > 0) {
        setCantDeleteOpen(true);
      } else {
        setDeleteDepartmentOpen(true);
      }
    } else if (key === MemberMenuKeys.deleteLog) {
      // 删除记录
      deleteRecord(record.id);
    } else if (key === MemberMenuKeys.deleteMember) {
      // 移除成员
      setDeleteMemberOpen(true);
    } else if (key === MemberMenuKeys.disableMember) {
      // 禁用成员
      // --------------------------------------------------
      setDisableUserOpen(true);
    } else if (key === MemberMenuKeys.divider) {
      // 分割线
    } else if (key === MemberMenuKeys.editDepartment) {
      // 修改部门
      setEditNameOpen(true);
    } else if (key === MemberMenuKeys.editNickName) {
      // 修改昵称
      setEditNickNameOpen(true);
    } else if (key === MemberMenuKeys.fileHandover) {
      // 文件交接
      // if (!authSuccess) {
      //   // 1. 如果没有进入管理员模式
      //   // 2. 打开授权对话框 1 手机验证码 2 邮箱验证 3 密码验证
      //   openAuthModal?.({
      //     onSuccess: () => {
      //       setFileHandoverOpen(true);
      //     },
      //   });
      // } else {
      //   setFileHandoverOpen(true);
      // }
    } else if (key === MemberMenuKeys.launchFileHandover || key === MemberMenuKeys.manageFileHandover) {
      // 发起文件交接
      // if (!authSuccess) {
      //   // 1. 如果没有进入管理员模式
      //   // 2. 打开授权对话框 1 手机验证码 2 邮箱验证 3 密码验证
      //   openAuthModal?.({
      //     onSuccess: () => {
      //       setFileHandoverOpen(true);
      //     },
      //   });
      // } else {
      //   setFileHandoverOpen(true);
      // }
    } else if (key === MemberMenuKeys.fileHandoverHistory) {
      // 查看文件交接历史
      // if (!authSuccess) {
      //   // 1. 如果没有进入管理员模式
      //   // 2. 打开授权对话框 1 手机验证码 2 邮箱验证 3 密码验证
      //   openAuthModal?.({
      //     onSuccess: () => {
      //       setFileHandoverHistoryOpen(true);
      //     },
      //   });
      // } else {
      //   setFileHandoverHistoryOpen(true);
      // }
    } else if (key === MemberMenuKeys.inviteJoinEnterprise) {
      // 邀请该用户加入企业 特殊 情况该用户已加入其他企业，无法邀请
      setInviteJoinOpen(true);
    } else if (key === MemberMenuKeys.reactivateMember) {
      if (syncPanelEnabled) {
        setActivateOrDisableOpen(true);
      } else {
        // 重新激活成员
        setReactiveOpen(true);
      }
    } else if (key === MemberMenuKeys.removeFromAllEnterpriseFiles) {
      // 将该用户从所有企业文件中移除
      setRemoveOutersOpen(true);
    } else if (key === MemberMenuKeys.restPassword) {
      // 重置密码
      // if (featureDisabled) {
      //   return;
      // }
      if (!authSuccess) {
        // 1. 如果没有进入管理员模式
        // 2. 打开授权对话框 1 手机验证码 2 邮箱验证 3 密码验证
        openAuthModal?.({
          onSuccess: () => {
            setRestPasswordOpen(true);
          },
        });
      } else {
        setRestPasswordOpen(true);
      }
    } else if (key === MemberMenuKeys.setDepartment) {
      // 设置所在部门
      setChangeDepartmentOpen(true);
    } else if (key === MemberMenuKeys.unbindEmail) {
      // 解绑邮箱
      if (!authSuccess) {
        // 1. 如果没有进入管理员模式
        // 2. 打开授权对话框 1 手机验证码 2 邮箱验证 3 密码验证
        openAuthModal?.({
          onSuccess: () => {
            setUnbindEmailOpen(true);
          },
        });
      } else {
        // 解绑邮箱
        setUnbindEmailOpen(true);
      }
    }
  };

  return (
    <div className={styles['styledRight']}>
      <RightHeader
        activeDepartment={activeDepartment}
        changeActiveRangeOpen={(v: boolean) => {
          setActiveRangeOpen(v);
        }}
        changeWechatLink={(v: string) => {
          setWechatDownloadLink(v);
        }}
        changeWechatLinkOpen={(v: boolean) => {
          setWechatCopyLinkOpen(v);
        }}
      />
      {!activeOtherKey && !isInSearchMode && (
        <OperationContainer
          activeDepartmentId={activeDepartmentId}
          changeAddNewMemberOpen={(v: boolean) => {
            if (v && me && !checkAddNewMemberPermissions(me)) {
              // 权限判断
              setExpiredAddMemberOpen(v);
              return;
            }
            setAddNewMemberOpen(v);
          }}
          changeDepartmentOpen={(v: boolean) => {
            setChangeDepartmentOpen(v);
          }}
          changeInviteOpen={(v: boolean) => {
            if (v && me && !checkAddNewMemberPermissions(me)) {
              // 权限判断
              setExpiredAddMemberOpen(v);
              return;
            }
            setInviteOpen(v);
          }}
          changeInviteType={(v: InviteType) => {
            // if (v && me && !checkAddNewMemberPermissions(me)) {
            //   // 权限判断
            //   setExpiredAddMemberOpen(v);
            //   return;
            // }
            setInviteType(v);
          }}
          removeFromDepartment={removeFromDepartment}
          selectedUsers={selectedUsers}
        />
      )}
      {(!activeOtherKey || isInSearchMode) && (
        <DepartmentTable
          activeDepartment={activeDepartment}
          changeActiveDepartmentId={changeActiveDepartmentId}
          changeCurrentTableDepartment={(v: Department | null) => {
            setCurrentTableDepartment(v);
          }}
          changeCurrentTableUser={(user: UserItem | null) => {
            setCurrentTableUser(user);
          }}
          changeSelectedUsers={(userList: UserItem[]) => {
            setSelectedUsers(userList);
          }}
          handleContextMenuClick={handleContextMenuClick}
          initLoading={initLoading}
          selectedUsers={selectedUsers}
        />
      )}
      {!isInSearchMode && activeOtherKey && (
        <OuterTable
          changeCurrentTableUser={(user: UserItem | null) => {
            setCurrentTableUser(user);
          }}
          handleContextMenuClick={handleContextMenuClick}
        />
      )}

      {addNewMemberOpen && (
        <AddNewMemberModal
          activeDepartmentId={activeDepartmentId}
          changeOpen={(v: boolean) => {
            setAddNewMemberOpen(v);
          }}
          open={addNewMemberOpen}
        />
      )}
      {inviteOpen && (
        <InviteModal
          activeDepartmentId={activeDepartmentId}
          changeOpen={(v: boolean) => {
            setInviteOpen(v);
          }}
          inviteType={inviteType}
          open={inviteOpen}
        />
      )}
      {changeDepartmentOpen && (
        <ChangeDepartmentModal
          changeOpen={(v: boolean) => {
            if (!v) {
              setCurrentTableUser(null);
            }
            setChangeDepartmentOpen(v);
          }}
          open={changeDepartmentOpen}
          selectedUsers={selectedUsers}
          user={currentTableUser}
        />
      )}
      {deleteMemberOpen && (
        <DeleteMemberModal
          activeDepartmentId={activeDepartmentId}
          changeOpen={(v: boolean) => {
            if (!v) {
              setCurrentTableUser(null);
            }
            setDeleteMemberOpen(v);
          }}
          modal={modal}
          open={deleteMemberOpen}
          user={currentTableUser}
        />
      )}
      {unbindEmailOpen && (
        <UnBindEmailModal
          activeDepartmentId={activeDepartmentId}
          changeOpen={(v: boolean) => {
            if (!v) {
              setCurrentTableUser(null);
            }
            setUnbindEmailOpen(v);
          }}
          open={unbindEmailOpen}
          user={currentTableUser}
        />
      )}
      {cancelInviteOpen && (
        <CancelInviteModal
          activeDepartmentId={activeDepartmentId}
          changeOpen={(v: boolean) => {
            if (!v) {
              setCurrentTableUser(null);
            }
            setCancelInviteOpen(v);
          }}
          open={cancelInviteOpen}
          user={currentTableUser}
        />
      )}
      {inviteJoinOpen && (
        <InviteJoinModal
          changeOpen={(v: boolean) => {
            setInviteJoinOpen(v);
          }}
          open={inviteJoinOpen}
          user={currentTableUser}
        />
      )}
      {(reactiveOpen || activateOpen) && (
        <ReactivatedModal
          activeDepartmentId={activeDepartmentId}
          changeOpen={(v: boolean) => {
            if (reactiveOpen && !activateOpen) {
              setReactiveOpen(v);
            } else if (!reactiveOpen && activateOpen) {
              setActivateOpen(v);
            }
          }}
          isReactive={reactiveOpen && !activateOpen}
          open={reactiveOpen || activateOpen}
          user={currentTableUser}
        />
      )}
      {disableUserOpen && (
        <DisableUserModal
          changeOpen={(v: boolean) => {
            if (!v) {
              setCurrentTableDepartment(null);
            }
            setDisableUserOpen(v);
          }}
          open={disableUserOpen}
          user={currentTableUser}
        />
      )}
      {removeOutersOpen && (
        <RemoveOutersModal
          changeOpen={(v: boolean) => {
            setRemoveOutersOpen(v);
          }}
          open={removeOutersOpen}
          user={currentTableUser}
        />
      )}
      {checkFileOpen && (
        <CheckFileModal
          changeOpen={(v: boolean) => {
            setCheckFileOpen(v);
          }}
          open={checkFileOpen}
          user={currentTableUser}
        />
      )}
      {activateOrDisableOpen && (
        <ActiveOrDisableModal
          changeActiveRangeOpen={(v: boolean) => {
            setActiveRangeOpen(v);
          }}
          changeOpen={(v: boolean) => {
            setActivateOrDisableOpen(v);
          }}
          open={activateOrDisableOpen}
        />
      )}
      {dingdingTipOpen && (
        <DingdingTipModal
          changeOpen={(v: boolean) => {
            setDingdingTipOpen(v);
          }}
          open={dingdingTipOpen}
        />
      )}
      {expiredAddMemberOpen && (
        <ExpiredAddMemberModal
          changeOpen={(v: boolean) => {
            setExpiredAddMemberOpen(v);
          }}
          isPayingExpired={isPayingExpired}
          open={expiredAddMemberOpen}
        />
      )}
      {wechatCopyLinkOpen && (
        <WechatCopyLinkModal
          changeOpen={(v: boolean) => {
            setWechatCopyLinkOpen(v);
          }}
          link={wechatDownloadLink}
          open={wechatCopyLinkOpen}
        />
      )}
      {activeRangeOpen && (
        <ActiveRangeModalWithProvider
          activeDepartmentId={activeDepartmentId}
          changeOpen={(v: boolean) => {
            setActiveRangeOpen(v);
          }}
          open={activeRangeOpen}
        />
      )}
      {/* {currentTableUser && fileHandoverHistoryOpen && (
        <FileHandoverHistoryModal
          id={currentTableUser.id}
          name={currentTableUser.name}
          avatar={currentTableUser.avatar}
          onCancel={() => {
            setFileHandoverHistoryOpen(false);
          }}
        />
      )} */}
      {/* {currentTableUser && fileHandoverOpen && (
        <FileHandoverModal
          isModal
          id={currentTableUser.id}
          name={currentTableUser.name}
          avatar={currentTableUser.avatar}
          demission={
            activeOtherKey === OtherListKeys.Disabled ||
            currentTableUser.teamRole === TeamRole.disabled
          }
          onCancel={() => {
            setFileHandoverOpen(false);
          }}
        />
      )} */}
      {addSubDepartmentOpen && (
        <AddSubDepartmentModal
          changeOpen={(v: boolean) => {
            setAddSubDepartmentOpen(v);
          }}
          open={addSubDepartmentOpen}
          parentDepartment={currentTableDepartment}
        />
      )}
      {editNameOpen && (
        <EditDepartmentNameModal
          changeOpen={(v: boolean) => {
            setEditNameOpen(v);
          }}
          department={currentTableDepartment}
          open={editNameOpen}
        />
      )}
      {deleteDepartmentOpen && currentTableDepartment && (
        <DeleteDepartmentModal
          changeOpen={(v: boolean) => {
            setDeleteDepartmentOpen(v);
          }}
          department={currentTableDepartment}
          open={deleteDepartmentOpen}
        />
      )}
      {removeFromDepartmentOpen && (
        <RemoveFromDepartmentModal
          activeDepartmentId={activeDepartmentId}
          changeOpen={(v: boolean) => {
            setRemoveFromDepartmentOpen(v);
          }}
          departmentName={activeDepartment?.name ?? ''}
          open={removeFromDepartmentOpen}
          selectedUsers={selectedUsers}
        />
      )}
      <CantDeleteModal
        changeOpen={(v: boolean) => {
          setCantDeleteOpen(v);
        }}
        open={cantDeleteOpen}
      />
      <BindEmailModal
        changeOpen={(v: boolean) => {
          if (!v) {
            setCurrentTableUser(null);
          }
          setBindEmailOpen(v);
        }}
        currentTableUser={currentTableUser}
        open={bindEmailOpen}
      />
      <RestPasswordModal
        changeOpen={(v: boolean) => {
          if (!v) {
            setCurrentTableUser(null);
          }

          setRestPasswordOpen(v);
        }}
        currentTableUser={currentTableUser}
        open={restPasswordOpen}
      />
      <EditNickNameModal
        activeDepartmentId={activeDepartmentId}
        changeOpen={(v: boolean) => {
          if (!v) {
            setCurrentTableUser(null);
          }
          setEditNickNameOpen(v);
        }}
        currentTableUser={currentTableUser}
        open={editNickNameOpen}
      />
      {contextHolder}
    </div>
  );
};
