import s18n from '@shimo/simple-i18n';
import { Tooltip } from 'antd';
import React, { useContext } from 'react';

import { MembersContext } from '@/contexts/members';
import { type ItemSource, OtherListKeys } from '@/contexts/members/type';
import { fm } from '@/modules/Locale';

import styles from './RightHeader.less';

interface Props {
  activeDepartment: ItemSource | null;
  changeActiveRangeOpen: (v: boolean) => void;
  changeWechatLink: (v: string) => void;
  changeWechatLinkOpen: (v: boolean) => void;
}

export const RightHeader: React.FC<Props> = ({ activeDepartment }) => {
  const { activeOtherKey, disableTotal, outsiderTotal, onlyOnRootTotal, unactivated, isInSearchMode, searchUserList } =
    useContext(MembersContext);

  const data = (() => {
    if (!activeOtherKey) {
      return activeDepartment;
    }
    if (activeOtherKey === OtherListKeys.OnlyOnRoot) {
      return {
        name: fm('Members.onlyOnRoot'),
        allMemberCount: onlyOnRootTotal,
        children: null,
      };
    } else if (activeOtherKey === OtherListKeys.OuterSider) {
      return {
        name: fm('Members.outerSider'),
        allMemberCount: outsiderTotal,
        children: null,
      };
    } else if (activeOtherKey === OtherListKeys.Disabled) {
      return {
        name: s18n('Members.disableder'),
        allMemberCount: disableTotal,
        children: null,
      };
    } else if (activeOtherKey === OtherListKeys.Inactivated) {
      return {
        name: s18n('Members.unactivated'),
        allMemberCount: unactivated.length,
        children: null,
      };
    }

    return null;
  })();

  return (
    <div className={styles['styledLeftPart']}>
      {isInSearchMode ? (
        <div className={styles['styledDeparmentText']}>
          {fm('Members.searchResults', { results: searchUserList.length ?? 0 })}
        </div>
      ) : (
        <>
          <Tooltip placement="top" title={data?.name}>
            <div className={styles['styledDeparmentText']}>{data?.name}</div>
          </Tooltip>
          {data?.children && (
            <div className={styles['styledLabelContainer']}>
              <div className={styles['styledLabelTitle']}>{fm('Members.subDepartment')}：</div>
              <div className={styles['styledLabelValue']}>{data.children.length}</div>
            </div>
          )}
          {data && data.allMemberCount > -1 && (
            <div className={styles['styledLabelContainer']}>
              <div className={styles['styledLabelTitle']}>{fm('Members.totalNumber')}：</div>
              <div className={styles['styledLabelValue']}>{data.allMemberCount}</div>
            </div>
          )}
        </>
      )}
    </div>
  );
};
