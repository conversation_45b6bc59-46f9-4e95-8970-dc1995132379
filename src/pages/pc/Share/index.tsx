import { MoreOutlined } from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import { Card, message, Space, Table, Typography } from 'antd';
import { useEffect, useMemo, useState } from 'react';

import { files } from '@/api/File';
import { ReactComponent as DesktopSvg } from '@/assets/images/empty/desktop.svg';
import CollaborationShare from '@/components/Collaboration';
import { createMenu } from '@/components/ContextMenu';
import { FileName } from '@/components/fileList/components/FileName';
import { NoData } from '@/components/fileList/components/NoData';
import RenameModal from '@/components/fileList/components/RenameModal';
import styles from '@/components/fileList/index.less';
import { useFileTypeDownload } from '@/hooks/useFileTypeDownload';
import { useTableMaxHeight } from '@/hooks/useTableMaxHeight';
import { fm } from '@/modules/Locale';
import { useThemeStore } from '@/store/Theme';
import { openFile, useFormatTime } from '@/utils/file';

import { items } from './items';

type DataType = {
  guid: string;
  updatedAt: number;
  createdAt: number;
  name: string;
  type: string;
  isSpace: boolean;
  [key: string]: any;
};

const Share = () => {
  const { isDark } = useThemeStore((state) => state.theme);

  const { downloadDiffFile } = useFileTypeDownload();

  const { formatTime } = useFormatTime();

  const i18nText = {
    newTabOpens: fm('File.newTabOpens'),
    star: fm('File.star'),
    starSuccess: fm('File.starSuccess'),
    starError: fm('File.starError'),
    removeStr: fm('File.removeStar'),
    removeStarSuccess: fm('File.removeSuccess'),
    removeStarError: fm('File.removeError'),
    share: fm('File.share'),
    view: fm('File.view'),
    collaboration: fm('File.collaboration'),
    download: fm('File.download'),
    reName: fm('File.reName'),
    png: fm('File.png'),
    moveTo: fm('File.moveTo'),
    copyTo: fm('File.copyTo'),
    copy: fm('FilePathPicker.copy'),
    delete: fm('File.delete'),
    clearFilter: fm('File.clearFilter'),
    recentlyOpened: fm('File.recentlyOpened'),
    recentlyEdit: fm('File.recentlyEdit'),
    title: `${fm('deleteConfirm.title')}?`,
    content: fm('deleteConfirm.content'),
    okText: fm('deleteConfirm.title'),
    cancelText: fm('deleteConfirm.cancel'),
    success: fm('File.deleteSuccess'),
    error: fm('File.deleteError'),
    downloadSuccess: fm('File.downloadSuccess'),
    downloadError: fm('File.downloadError'),
    noShareTitle: fm('File.noShareTitle'),
    noShareDescription: fm('File.noShareDescription'),
  };
  const [data, setData] = useState<DataType[]>([]);

  const [noData, setNoData] = useState(false);

  const [loading, setLoading] = useState<boolean>(false);

  const [renameVisible, setRenameVisible] = useState(false);

  const [params, setParams] = useState<{ FileName?: string; guid?: string }>({ FileName: '', guid: '' });

  const { tableMaxHeight, tableClassName } = useTableMaxHeight({});

  const [shareVisible, setShareVisible] = useState(false);

  const [shareData, setShareData] = useState<any>();

  const reload = () => {
    setLoading(true);
    files({ type: 'shared' })
      .then((res: any) => {
        if (res.status === 200) {
          const { list = [] } = res.data;
          setData(list);
          setNoData(list ? list.length === 0 : true);
        }
      })
      .catch((error) => {
        message.error(error.data?.msg);
        setNoData(true);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const renameCallback = ({
    visible,
    params,
    refresh,
  }: {
    visible: boolean;
    params?: { fileName?: string; guid?: string };
    refresh?: boolean;
  }) => {
    setRenameVisible(visible);
    setParams(params || {});
    if (refresh) reload();
  };

  const handleContextMenu = (e: React.MouseEvent, record: any) => {
    e.preventDefault();
    setShareData(record);
    createMenu.show({
      position: { x: e.clientX, y: e.clientY },
      items: items({
        record: record,
        i18nText,
        renameCallback,
        reload,
        setShareVisible,
        downloadDiffFile,
      }),
      theme: isDark ? 'dark' : 'light',
    });
  };

  const columns: TableColumnsType = [
    {
      title: fm('File.fileName'),
      dataIndex: 'name',
      minWidth: 160,
      render: (value: string, record: any) => {
        return <FileName name={value} record={record} />;
      },
    },
    {
      title: fm('File.createName'),
      dataIndex: ['user', 'name'],
      width: 120,
      ellipsis: true,
    },
    {
      title: fm('File.shareInfo'),
      dataIndex: ['user', 'name'],
      width: 260,
      render: (value: any, record: any) => {
        return (
          <Space>
            <span>{formatTime(record.createdAt * 1000)}</span>
            <Typography.Text className="share-user" ellipsis={{ tooltip: value }} style={{ maxWidth: 60 }}>
              {value}
            </Typography.Text>
            <span>{fm('File.share')}</span>
          </Space>
        );
      },
    },
    {
      title: '',
      dataIndex: 'options',
      width: 120,
      render: (value: any, record: object) => {
        return (
          <MoreOutlined
            className="more"
            onClick={(event) => {
              event.stopPropagation();
              handleContextMenu(event, record);
            }}
          />
        );
      },
    },
  ];

  const sortedList = useMemo(() => {
    const spaceData = data
      .filter((item) => item.type === 'folder' && item.isSpace)
      .sort((a, b) => a['createdAt'] - b['createdAt']);
    const folderData = data
      .filter((item) => item.type === 'folder' && !item.isSpace)
      .sort((a, b) => a['createdAt'] - b['createdAt']);
    const other = data.filter((item) => item.type !== 'folder').sort((a, b) => a['createdAt'] - b['createdAt']);
    return [...spaceData, ...folderData, ...other];
  }, [data]);

  useEffect(() => {
    reload();
  }, []);

  return (
    <Card className={styles['mainCardTable']} title={fm('SiderMenu.siderMenuShareText')}>
      {!noData ? (
        <Table
          virtual
          className={tableClassName}
          columns={columns}
          dataSource={sortedList}
          loading={loading}
          pagination={false}
          scroll={{ y: tableMaxHeight, x: 760 }}
          onRow={(record: any) => {
            return {
              onDoubleClick: () => {
                const newArg = record;
                if (record.isShortcut) {
                  const { url, type } = record.shortcutSource;
                  newArg.type = type;
                  newArg.url = url;
                }
                openFile(newArg);
              },
              onContextMenu: (event) => {
                handleContextMenu(event, record);
              },
            };
          }}
        />
      ) : (
        <NoData description={i18nText.noShareDescription} img={<DesktopSvg />} title={i18nText.noShareTitle} />
      )}
      <RenameModal callback={renameCallback} params={params} visible={renameVisible} />
      <CollaborationShare guid={shareData?.guid ?? ''} visible={shareVisible} onCancel={() => setShareVisible(false)} />
    </Card>
  );
};

export default Share;
